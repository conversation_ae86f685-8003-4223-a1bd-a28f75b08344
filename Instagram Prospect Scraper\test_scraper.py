"""
Test script for Instagram Prospect Scraper
"""

import sys
import logging
from instagram_scraper import InstagramScraper
from prospect_analyzer import ProspectAnalyzer
from data_exporter import DataExporter

def test_basic_functionality():
    """Test basic functionality of the scraper components"""
    print("Testing Instagram Prospect Scraper...")
    print("=" * 50)
    
    # Test 1: Initialize components
    print("1. Initializing components...")
    try:
        scraper = InstagramScraper(use_selenium=False)
        analyzer = ProspectAnalyzer()
        exporter = DataExporter()
        print("✓ All components initialized successfully")
    except Exception as e:
        print(f"✗ Error initializing components: {e}")
        return False
    
    # Test 2: Test proxy connectivity
    print("\n2. Testing proxy connectivity...")
    try:
        # Test a simple request
        response = scraper._make_request("https://httpbin.org/ip")
        if response and response.status_code == 200:
            print("✓ Proxy connectivity working")
            print(f"  Current IP: {response.json().get('origin', 'Unknown')}")
        else:
            print("✗ Proxy connectivity failed")
    except Exception as e:
        print(f"✗ Proxy test error: {e}")
    
    # Test 3: Test profile data parsing
    print("\n3. Testing profile data parsing...")
    try:
        # Test with sample data
        sample_profile = {
            'username': 'test_user',
            'full_name': 'Test User',
            'biography': 'Business coach helping entrepreneurs scale their business. Book a call!',
            'edge_followed_by': {'count': 15000},
            'edge_follow': {'count': 1500},
            'edge_owner_to_timeline_media': {'count': 250},
            'external_url': 'https://calendly.com/test-user',
            'is_private': False,
            'is_verified': False
        }
        
        parsed = scraper._parse_profile_data(sample_profile, 'test_user')
        print("✓ Profile data parsing working")
        print(f"  Parsed followers: {parsed.get('followers', 0):,}")
        print(f"  Bio: {parsed.get('bio', '')[:50]}...")
    except Exception as e:
        print(f"✗ Profile parsing error: {e}")
    
    # Test 4: Test prospect analysis
    print("\n4. Testing prospect analysis...")
    try:
        sample_posts = [
            {'likes': 150, 'comments': 12, 'caption': 'Client success story! Amazing transformation.'},
            {'likes': 200, 'comments': 18, 'caption': 'Ready to scale your business? DM me!'},
            {'likes': 180, 'comments': 15, 'caption': 'New program launching soon. Limited spots available.'}
        ]
        
        analysis = analyzer.analyze_profile(parsed, sample_posts)
        print("✓ Prospect analysis working")
        print(f"  Qualified: {analysis.get('qualified', False)}")
        print(f"  Engagement rate: {analysis.get('engagement_rate', 0):.2f}%")
        print(f"  High-ticket qualified: {analysis.get('high_ticket_qualified', False)}")
        print(f"  Quality score: {analysis.get('quality_score', 0):.1f}")
    except Exception as e:
        print(f"✗ Analysis error: {e}")
    
    # Test 5: Test data export
    print("\n5. Testing data export...")
    try:
        test_prospects = [analysis] if 'analysis' in locals() else []
        if test_prospects:
            csv_file = exporter.export_to_csv(test_prospects, 'test_export.csv')
            print(f"✓ CSV export working: {csv_file}")
            
            excel_file = exporter.export_to_excel(test_prospects, 'test_export.xlsx')
            print(f"✓ Excel export working: {excel_file}")
        else:
            print("⚠ Skipping export test (no test data)")
    except Exception as e:
        print(f"✗ Export error: {e}")
    
    # Test 6: Test count parsing
    print("\n6. Testing count parsing...")
    try:
        test_counts = ['1.2M', '15.3K', '500', '2,500']
        for count_str in test_counts:
            parsed_count = scraper._parse_count(count_str)
            print(f"  {count_str} -> {parsed_count:,}")
        print("✓ Count parsing working")
    except Exception as e:
        print(f"✗ Count parsing error: {e}")
    
    # Cleanup
    try:
        scraper.close()
        print("\n✓ Cleanup completed")
    except Exception as e:
        print(f"\n⚠ Cleanup warning: {e}")
    
    print("\n" + "=" * 50)
    print("Test completed! Check the results above.")
    print("If all tests pass, the scraper is ready to use.")
    return True

def test_live_scraping():
    """Test live scraping with a single username (optional)"""
    print("\nLive Scraping Test (Optional)")
    print("=" * 30)
    
    test_username = input("Enter a username to test (or press Enter to skip): ").strip()
    if not test_username:
        print("Skipping live test.")
        return
    
    print(f"Testing live scraping for: @{test_username}")
    
    try:
        scraper = InstagramScraper(use_selenium=False)
        analyzer = ProspectAnalyzer()
        
        # Get profile data
        print("Fetching profile data...")
        profile_data = scraper.get_profile_data(test_username)
        
        if not profile_data:
            print("✗ Could not fetch profile data")
            return
        
        print(f"✓ Profile data fetched")
        print(f"  Followers: {profile_data.get('followers', 0):,}")
        print(f"  Bio: {profile_data.get('bio', '')[:100]}...")
        
        # Get posts
        print("Fetching recent posts...")
        posts_data = scraper.get_recent_posts(test_username, 5)
        print(f"✓ Fetched {len(posts_data)} posts")
        
        # Analyze
        print("Analyzing prospect...")
        analysis = analyzer.analyze_profile(profile_data, posts_data)
        
        print(f"✓ Analysis complete")
        print(f"  Qualified: {analysis.get('qualified', False)}")
        print(f"  Engagement rate: {analysis.get('engagement_rate', 0):.2f}%")
        print(f"  High-ticket indicators: {len(analysis.get('bio_keywords_found', []))}")
        
        scraper.close()
        
    except Exception as e:
        print(f"✗ Live test error: {e}")

if __name__ == "__main__":
    # Set up basic logging
    logging.basicConfig(level=logging.INFO)
    
    # Run basic tests
    test_basic_functionality()
    
    # Ask if user wants to run live test
    if len(sys.argv) > 1 and sys.argv[1] == '--live':
        test_live_scraping()
    else:
        print("\nTo test live scraping, run: python test_scraper.py --live")
