"""
Configuration file for Instagram Prospect Scraper
"""

import os
from typing import List, Dict

# Proxy Configuration
PROXIES = [
    {
        'http': '*****************************************************',
        'https': '*****************************************************'
    },
    {
        'http': '***************************************************',
        'https': '***************************************************'
    }
]

# Target Account Criteria
FOLLOWER_COUNT_MIN = 1000
FOLLOWER_COUNT_MAX = 50000

# Engagement Criteria
MIN_ENGAGEMENT_RATE = 1.0  # Minimum 1% engagement rate
MIN_AVERAGE_LIKES_PERCENTAGE = 1.0  # 1% of followers
MIN_AVERAGE_COMMENTS = 5  # Minimum 5 comments on average
POSTS_TO_ANALYZE = 10  # Number of recent posts to analyze for engagement

# High-Ticket Offer Keywords
HIGH_TICKET_BIO_KEYWORDS = [
    'coaching', 'consultant', 'consulting', 'program', 'mastermind',
    '1:1', 'one-on-one', 'apply now', 'book a call', 'signature course',
    'high-ticket', 'premium', 'exclusive', 'transformation', 'breakthrough',
    'strategy session', 'discovery call', 'application', 'enrollment',
    'mentorship', 'accelerator', 'intensive', 'blueprint', 'system',
    'framework', 'method', 'certification', 'training', 'workshop',
    'seminar', 'bootcamp', 'academy', 'university', 'institute',
    'canada', 'canadian', 'toronto', 'vancouver', 'calgary', 'montreal',
    'ottawa', 'edmonton', 'winnipeg', 'quebec', 'ontario', 'british columbia',
    'alberta', 'bc', 'on', 'ab', 'qc', 'mb', 'sk', 'ns', 'nb', 'pe', 'nl'
]

HIGH_TICKET_POST_KEYWORDS = [
    'client results', 'testimonial', 'success story', 'transformation',
    'breakthrough', 'program', 'enrollment', 'spots available',
    'limited time', 'application', 'discovery call', 'strategy session',
    'book a call', 'dm me', 'link in bio', 'swipe up', 'comment below',
    'ready to invest', 'investment', 'ROI', 'return on investment'
]

# Link indicators for high-ticket offers
HIGH_TICKET_LINK_DOMAINS = [
    'calendly.com', 'acuity', 'typeform.com', 'jotform.com',
    'linktree.com', 'beacons.ai', 'linktr.ee', 'bio.link',
    'clickfunnels.com', 'leadpages.com', 'unbounce.com',
    'thrivecart.com', 'kajabi.com', 'teachable.com',
    'podia.com', 'gumroad.com', 'samcart.com'
]

# Niche-specific keywords for targeting
NICHE_KEYWORDS = [
    'business coach', 'life coach', 'marketing consultant', 'course creator',
    'entrepreneur', 'business mentor', 'success coach', 'mindset coach',
    'sales coach', 'leadership coach', 'executive coach', 'career coach',
    'wellness coach', 'fitness coach', 'nutrition coach', 'relationship coach',
    'spiritual coach', 'manifestation coach', 'abundance coach', 'money coach',
    'financial advisor', 'investment coach', 'real estate coach', 'trading coach'
]

# Hashtags for scraping (Canada-focused)
TARGET_HASHTAGS = [
    '#canadiancoach', '#torontobusinesscoach', '#vancouverlifecoach', '#canadianconsultant',
    '#businesscoachcanada', '#lifecoachcanada', '#consultantcanada', '#canadianentrepreneur',
    '#torontocoach', '#vancouvercoach', '#calgarycoach', '#montrealcoach', '#ottawacoach',
    '#canadianbusiness', '#canadianmentor', '#torontoconsultant', '#vancouverconsultant',
    '#businesscoachtoronto', '#lifecoachvancouver', '#mindsetcoachcanada', '#salescoachcanada',
    '#leadershipcoachcanada', '#executivecoachcanada', '#careercoachcanada', '#wellnesscoachcanada',
    '#fitnesscoachcanada', '#nutritioncoachcanada', '#relationshipcoachcanada', '#spiritualcoachcanada',
    '#manifestationcoachcanada', '#abundancecoachcanada', '#moneycoachcanada', '#financialadvisorcanada',
    '#investmentcoachcanada', '#realestatecoachcanada', '#tradingcoachcanada', '#coursecreatorcanada',
    '#onlinecoachcanada', '#digitalmarketingcanada', '#socialmediamarketingcanada', '#contentcreatorcanada'
]

# Scraping Settings
REQUEST_DELAY = 2  # Seconds between requests
MAX_RETRIES = 3
TIMEOUT = 30  # Request timeout in seconds
USER_AGENTS = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15'
]

# Output Settings
OUTPUT_DIR = 'scraped_data'
CSV_FILENAME_PREFIX = 'instagram_prospects'
EXCEL_FILENAME_PREFIX = 'instagram_prospects'

# Logging Configuration
LOG_LEVEL = 'INFO'
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
LOG_FILE = 'instagram_scraper.log'

# Instagram URLs
INSTAGRAM_BASE_URL = 'https://www.instagram.com'
INSTAGRAM_EXPLORE_TAGS_URL = f'{INSTAGRAM_BASE_URL}/explore/tags'
INSTAGRAM_GRAPHQL_URL = f'{INSTAGRAM_BASE_URL}/graphql/query'

# Rate Limiting
REQUESTS_PER_MINUTE = 30
REQUESTS_PER_HOUR = 1000
