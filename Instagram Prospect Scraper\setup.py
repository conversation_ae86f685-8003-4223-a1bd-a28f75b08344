"""
Setup script for Instagram Prospect Scraper
"""

import subprocess
import sys
import os

def install_requirements():
    """Install required packages"""
    print("Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ Requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Error installing requirements: {e}")
        return False

def check_chrome():
    """Check if Chrome is installed (for Selenium)"""
    print("Checking Chrome installation...")
    try:
        # Try to find Chrome executable
        import platform
        system = platform.system()
        
        if system == "Windows":
            chrome_paths = [
                r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe"
            ]
        elif system == "Darwin":  # macOS
            chrome_paths = ["/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"]
        else:  # Linux
            chrome_paths = ["/usr/bin/google-chrome", "/usr/bin/chromium-browser"]
        
        chrome_found = any(os.path.exists(path) for path in chrome_paths)
        
        if chrome_found:
            print("✓ Chrome found")
            return True
        else:
            print("⚠ Chrome not found - Selenium features may not work")
            print("  Please install Google Chrome for full functionality")
            return False
            
    except Exception as e:
        print(f"⚠ Could not check Chrome: {e}")
        return False

def test_proxy_connection():
    """Test proxy connection"""
    print("Testing proxy connection...")
    try:
        import requests
        from config import PROXIES
        
        proxy = PROXIES[0]  # Test first proxy
        response = requests.get("https://httpbin.org/ip", proxies=proxy, timeout=10)
        
        if response.status_code == 200:
            ip_info = response.json()
            print(f"✓ Proxy working - IP: {ip_info.get('origin', 'Unknown')}")
            return True
        else:
            print(f"✗ Proxy test failed - Status: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"✗ Proxy test error: {e}")
        return False

def create_directories():
    """Create necessary directories"""
    print("Creating directories...")
    try:
        os.makedirs("scraped_data", exist_ok=True)
        print("✓ Directories created")
        return True
    except Exception as e:
        print(f"✗ Error creating directories: {e}")
        return False

def run_basic_test():
    """Run basic functionality test"""
    print("Running basic functionality test...")
    try:
        from test_scraper import test_basic_functionality
        test_basic_functionality()
        return True
    except Exception as e:
        print(f"✗ Basic test failed: {e}")
        return False

def main():
    """Main setup function"""
    print("Instagram Prospect Scraper - Setup")
    print("=" * 40)
    
    success_count = 0
    total_tests = 5
    
    # 1. Install requirements
    if install_requirements():
        success_count += 1
    
    # 2. Check Chrome
    if check_chrome():
        success_count += 1
    
    # 3. Create directories
    if create_directories():
        success_count += 1
    
    # 4. Test proxy
    if test_proxy_connection():
        success_count += 1
    
    # 5. Run basic test
    if run_basic_test():
        success_count += 1
    
    # Summary
    print("\n" + "=" * 40)
    print("Setup Summary")
    print("=" * 40)
    print(f"Tests passed: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("✓ Setup completed successfully!")
        print("\nYou can now run the scraper:")
        print("  python main.py --mode hashtags")
        print("  python test_scraper.py")
        print("  python example_usage.py")
    elif success_count >= 3:
        print("⚠ Setup mostly successful with some warnings")
        print("  The scraper should work, but some features may be limited")
    else:
        print("✗ Setup failed - please check the errors above")
        print("  Try installing dependencies manually:")
        print("  pip install -r requirements.txt")

if __name__ == "__main__":
    main()
