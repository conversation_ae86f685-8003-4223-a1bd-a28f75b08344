# Quick Start Guide - Instagram Prospect Scraper

## 🚀 Get Started in 3 Steps

### Step 1: Setup
```bash
cd "Instagram Prospect Scraper"
python setup.py
```

### Step 2: Test
```bash
python test_scraper.py
```

### Step 3: Run
```bash
python main.py --mode hashtags
```

## 📊 What You'll Get

### Qualified Prospects with:
- ✅ 1K-50K followers
- ✅ Good engagement (1%+ rate)
- ✅ High-ticket offer indicators
- ✅ Public coaching/consulting accounts

### Output Files:
- **CSV**: All prospects with detailed metrics
- **Excel**: Multi-sheet analysis with summary
- **Logs**: Detailed scraping information

## 🎯 Common Use Cases

### 1. Quick Hashtag Scraping
```bash
python main.py --mode hashtags --hashtags businesscoach lifecoach consultant
```

### 2. Specific Username Analysis
```bash
python main.py --mode usernames --usernames user1 user2 user3
```

### 3. Mixed Approach
```bash
python main.py --mode mixed --usernames known_coach --hashtags businesscoach
```

### 4. High-Quality Scraping (Slower but More Reliable)
```bash
python main.py --mode hashtags --use-selenium
```

## 📈 Understanding Results

### Quality Score (0-100):
- **80-100**: Premium prospects
- **60-79**: Good prospects  
- **40-59**: Moderate prospects
- **Below 40**: Low priority

### Key Metrics:
- **Engagement Rate**: (Likes + Comments) / Followers × 100
- **High-Ticket Score**: Keywords and link indicators
- **Qualification Status**: Meets all criteria

## 🔧 Configuration

### Adjust Criteria (config.py):
```python
FOLLOWER_COUNT_MIN = 1000      # Minimum followers
FOLLOWER_COUNT_MAX = 50000     # Maximum followers
MIN_ENGAGEMENT_RATE = 1.0      # Minimum engagement %
MIN_AVERAGE_COMMENTS = 5       # Minimum avg comments
```

### Add Keywords:
```python
HIGH_TICKET_BIO_KEYWORDS = [
    'coaching', 'consultant', 'program',
    'your_custom_keyword'  # Add here
]
```

## 🛠️ Troubleshooting

### No Results Found?
- Check if hashtags are relevant
- Lower criteria in config.py
- Verify proxy connection

### Rate Limited?
- Increase REQUEST_DELAY in config.py
- Use --use-selenium flag
- Try different hashtags

### Selenium Issues?
- Install Google Chrome
- Check ChromeDriver compatibility
- Use requests mode instead

## 📁 File Structure

```
Instagram Prospect Scraper/
├── main.py              # Main scraper
├── config.py           # Settings
├── setup.py            # Installation
├── test_scraper.py     # Testing
├── example_usage.py    # Examples
├── scraped_data/       # Output folder
└── README.md          # Full documentation
```

## 🎯 Pro Tips

1. **Start Small**: Test with 1-2 hashtags first
2. **Check Logs**: Monitor instagram_scraper.log for issues
3. **Customize Keywords**: Add niche-specific terms
4. **Use Selenium**: For better reliability (slower)
5. **Export Excel**: Better for analysis and filtering

## 📞 Next Steps

1. Run the scraper with your target hashtags
2. Analyze the Excel output (Summary sheet)
3. Focus on qualified prospects with high scores
4. Use the bio keywords to understand their offers
5. Check their external links for contact methods

## ⚡ Quick Commands Reference

```bash
# Basic hashtag scraping
python main.py

# Custom hashtags
python main.py --hashtags businesscoach lifecoach

# Specific usernames
python main.py --mode usernames --usernames coach1 coach2

# High-quality mode
python main.py --use-selenium

# Test everything
python test_scraper.py

# See examples
python example_usage.py
```

---

**Ready to find your next high-value prospects? Start with the setup command above! 🎯**
