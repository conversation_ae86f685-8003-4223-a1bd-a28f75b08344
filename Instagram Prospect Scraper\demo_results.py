"""
Demo script showing expected results for US coaches scraping
"""

import json
import pandas as pd
from datetime import datetime
import os

def create_demo_results():
    """Create demo results showing what the scraper would find"""
    
    # Simulated results for US coaches that would be found
    demo_prospects = [
        {
            'username': 'businesscoach_usa',
            'profile_url': 'https://instagram.com/businesscoach_usa',
            'full_name': '<PERSON>',
            'follower_count': 15420,
            'following_count': 1250,
            'bio_text': 'Business Coach helping entrepreneurs scale to 7-figures 🚀 | USA Based | Book a strategy call ⬇️',
            'link_in_bio': 'https://calendly.com/sarah-johnson/strategy-call',
            'post_count': 342,
            'avg_likes': 185.4,
            'avg_comments': 12.8,
            'engagement_rate': 1.28,
            'posts_analyzed': 10,
            'high_ticket_qualified': True,
            'bio_keywords_found': ['business coach', 'entrepreneurs', 'usa', 'strategy call'],
            'post_keywords_found': ['client results', 'transformation', 'program'],
            'email_found': '',
            'link_analysis': {
                'domain': 'calendly.com',
                'is_high_ticket_domain': True,
                'link_type': 'booking'
            },
            'quality_score': 87.5,
            'qualified': True,
            'scraped_at': datetime.now().isoformat(),
            'follower_qualified': True,
            'engagement_qualified': True,
            'is_private': False,
            'is_verified': False
        },
        {
            'username': 'lifecoach_miami',
            'profile_url': 'https://instagram.com/lifecoach_miami',
            'full_name': 'Michael Rodriguez',
            'follower_count': 8750,
            'following_count': 890,
            'bio_text': 'Life Coach & Mindset Expert 🧠 | Miami, FL | Transform your life in 90 days | Apply below 👇',
            'link_in_bio': 'https://typeform.com/to/apply-coaching',
            'post_count': 156,
            'avg_likes': 142.3,
            'avg_comments': 18.6,
            'engagement_rate': 1.84,
            'posts_analyzed': 10,
            'high_ticket_qualified': True,
            'bio_keywords_found': ['life coach', 'mindset', 'miami', 'transform', 'apply'],
            'post_keywords_found': ['breakthrough', 'testimonial', 'limited spots'],
            'email_found': '',
            'link_analysis': {
                'domain': 'typeform.com',
                'is_high_ticket_domain': True,
                'link_type': 'application'
            },
            'quality_score': 92.1,
            'qualified': True,
            'scraped_at': datetime.now().isoformat(),
            'follower_qualified': True,
            'engagement_qualified': True,
            'is_private': False,
            'is_verified': False
        },
        {
            'username': 'consultant_nyc',
            'profile_url': 'https://instagram.com/consultant_nyc',
            'full_name': 'Jennifer Chen',
            'follower_count': 22100,
            'following_count': 1850,
            'bio_text': 'Marketing Consultant | NYC | Helping brands grow 10x | Book discovery call | <EMAIL>',
            'link_in_bio': 'https://linktree.com/jen-consultant',
            'post_count': 428,
            'avg_likes': 298.7,
            'avg_comments': 24.3,
            'engagement_rate': 1.46,
            'posts_analyzed': 10,
            'high_ticket_qualified': True,
            'bio_keywords_found': ['marketing consultant', 'nyc', 'discovery call'],
            'post_keywords_found': ['client success', 'ROI', 'investment'],
            'email_found': '<EMAIL>',
            'link_analysis': {
                'domain': 'linktree.com',
                'is_high_ticket_domain': True,
                'link_type': 'link_aggregator'
            },
            'quality_score': 89.3,
            'qualified': True,
            'scraped_at': datetime.now().isoformat(),
            'follower_qualified': True,
            'engagement_qualified': True,
            'is_private': False,
            'is_verified': True
        },
        {
            'username': 'success_coach_la',
            'profile_url': 'https://instagram.com/success_coach_la',
            'full_name': 'David Thompson',
            'follower_count': 12800,
            'following_count': 950,
            'bio_text': 'Success Coach | Los Angeles | Mindset & Business Breakthrough | Premium Coaching Program',
            'link_in_bio': 'https://kajabi.com/david-thompson-coaching',
            'post_count': 267,
            'avg_likes': 156.2,
            'avg_comments': 14.7,
            'engagement_rate': 1.33,
            'posts_analyzed': 10,
            'high_ticket_qualified': True,
            'bio_keywords_found': ['success coach', 'los angeles', 'mindset', 'business', 'premium', 'coaching program'],
            'post_keywords_found': ['transformation', 'breakthrough', 'enrollment'],
            'email_found': '',
            'link_analysis': {
                'domain': 'kajabi.com',
                'is_high_ticket_domain': True,
                'link_type': 'course_platform'
            },
            'quality_score': 85.7,
            'qualified': True,
            'scraped_at': datetime.now().isoformat(),
            'follower_qualified': True,
            'engagement_qualified': True,
            'is_private': False,
            'is_verified': False
        },
        {
            'username': 'entrepreneur_coach_tx',
            'profile_url': 'https://instagram.com/entrepreneur_coach_tx',
            'full_name': 'Amanda Williams',
            'follower_count': 18650,
            'following_count': 1420,
            'bio_text': 'Entrepreneur Coach | Dallas, TX | Scale to $1M+ | Exclusive Mastermind | DM "SCALE" for info',
            'link_in_bio': 'https://clickfunnels.com/amanda-williams-mastermind',
            'post_count': 389,
            'avg_likes': 234.8,
            'avg_comments': 19.2,
            'engagement_rate': 1.36,
            'posts_analyzed': 10,
            'high_ticket_qualified': True,
            'bio_keywords_found': ['entrepreneur coach', 'dallas', 'texas', 'scale', 'exclusive', 'mastermind'],
            'post_keywords_found': ['client results', 'success story', 'spots available'],
            'email_found': '',
            'link_analysis': {
                'domain': 'clickfunnels.com',
                'is_high_ticket_domain': True,
                'link_type': 'landing_page'
            },
            'quality_score': 88.9,
            'qualified': True,
            'scraped_at': datetime.now().isoformat(),
            'follower_qualified': True,
            'engagement_qualified': True,
            'is_private': False,
            'is_verified': False
        }
    ]
    
    return demo_prospects

def export_demo_results():
    """Export demo results to show expected output format"""
    
    prospects = create_demo_results()
    
    # Create DataFrame
    df = pd.DataFrame(prospects)
    
    # Ensure output directory exists
    os.makedirs('scraped_data', exist_ok=True)
    
    # Export to CSV
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    csv_filename = f"scraped_data/demo_us_coaches_{timestamp}.csv"
    df.to_csv(csv_filename, index=False)
    
    # Export to Excel with multiple sheets
    excel_filename = f"scraped_data/demo_us_coaches_{timestamp}.xlsx"
    with pd.ExcelWriter(excel_filename, engine='openpyxl') as writer:
        # All prospects
        df.to_excel(writer, sheet_name='All Prospects', index=False)
        
        # Qualified only
        qualified_df = df[df['qualified'] == True]
        qualified_df.to_excel(writer, sheet_name='Qualified Prospects', index=False)
        
        # Summary statistics
        summary_data = [
            ['Total Prospects', len(prospects)],
            ['Qualified Prospects', len(qualified_df)],
            ['Qualification Rate (%)', round(len(qualified_df)/len(prospects)*100, 1)],
            ['Average Followers (Qualified)', round(qualified_df['follower_count'].mean(), 0)],
            ['Average Engagement Rate (%)', round(qualified_df['engagement_rate'].mean(), 2)],
            ['Average Quality Score', round(qualified_df['quality_score'].mean(), 1)],
            ['', ''],
            ['Top Keywords Found:', ''],
            ['business coach', 3],
            ['entrepreneur', 2], 
            ['mindset', 2],
            ['usa/location based', 5],
            ['high-ticket indicators', 5]
        ]
        summary_df = pd.DataFrame(summary_data, columns=['Metric', 'Value'])
        summary_df.to_excel(writer, sheet_name='Summary', index=False)
    
    return csv_filename, excel_filename, prospects

def print_demo_results():
    """Print demo results to console"""
    
    prospects = create_demo_results()
    qualified = [p for p in prospects if p['qualified']]
    
    print("🎯 INSTAGRAM PROSPECT SCRAPER - DEMO RESULTS")
    print("=" * 60)
    print(f"Simulated search for US-based coaches")
    print(f"Total prospects found: {len(prospects)}")
    print(f"Qualified prospects: {len(qualified)}")
    print(f"Qualification rate: {len(qualified)/len(prospects)*100:.1f}%")
    
    print(f"\n📊 TOP QUALIFIED US COACHES:")
    print("-" * 60)
    
    # Sort by quality score
    sorted_prospects = sorted(qualified, key=lambda x: x['quality_score'], reverse=True)
    
    for i, prospect in enumerate(sorted_prospects, 1):
        print(f"{i}. @{prospect['username']}")
        print(f"   Name: {prospect['full_name']}")
        print(f"   Followers: {prospect['follower_count']:,}")
        print(f"   Engagement: {prospect['engagement_rate']:.1f}%")
        print(f"   Quality Score: {prospect['quality_score']:.1f}/100")
        print(f"   Bio: {prospect['bio_text'][:80]}...")
        print(f"   Link: {prospect['link_in_bio']}")
        print(f"   Keywords: {', '.join(prospect['bio_keywords_found'][:3])}")
        print()
    
    print("💡 INSIGHTS:")
    print("-" * 30)
    print("• All prospects have 1K-50K followers (target range)")
    print("• All have 1%+ engagement rates (good audience interaction)")
    print("• All have high-ticket indicators (booking links, programs)")
    print("• All are US-based (location keywords in bio)")
    print("• Mix of business, life, and success coaches")
    print("• Quality scores range from 85-92 (excellent prospects)")
    
    return prospects

def main():
    """Main demo function"""
    
    print("Instagram Prospect Scraper - Demo Results for US Coaches")
    print("=" * 70)
    print("This demonstrates the expected output when scraping for US coaches\n")
    
    # Print results to console
    prospects = print_demo_results()
    
    # Export files
    csv_file, excel_file, _ = export_demo_results()
    
    print(f"\n📁 EXPORTED FILES:")
    print("-" * 30)
    print(f"CSV: {csv_file}")
    print(f"Excel: {excel_file}")
    
    print(f"\n🚀 NEXT STEPS:")
    print("-" * 30)
    print("1. Run the actual scraper: python main.py --mode hashtags")
    print("2. Use US-focused hashtags: #businesscoach #lifecoach #usacoach")
    print("3. Analyze results in the Excel file (Summary sheet)")
    print("4. Contact qualified prospects through their booking links")
    
    print(f"\n✅ The scraper is configured and ready to find real US coaches!")

if __name__ == "__main__":
    main()
