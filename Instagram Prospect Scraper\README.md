# Instagram Prospect Scraper

A robust Instagram scraper designed to identify potential coaching and consulting prospects based on specific criteria including follower count, engagement rates, and high-ticket offer indicators.

## Features

- **Multi-Input Methods**: Scrape from usernames, hashtags, or both
- **Proxy Support**: Built-in proxy rotation for anonymity
- **Engagement Analysis**: Calculate engagement rates and analyze post performance
- **High-Ticket Detection**: Identify accounts with high-ticket offers through bio and link analysis
- **Prospect Qualification**: Comprehensive scoring system for prospect ranking
- **Data Export**: Export results to CSV and Excel with detailed analytics
- **Rate Limiting**: Intelligent rate limiting to avoid Instagram's anti-bot measures
- **Dual Scraping Methods**: Both requests-based and Selenium-based scraping

## Target Criteria

### Account Requirements
- **Niche**: Coaches, consultants, service providers
- **Follower Count**: 1,000 - 50,000 followers
- **Engagement**: Minimum 1% engagement rate
- **Privacy**: Public accounts only

### High-Ticket Indicators
- **Bio Keywords**: coaching, consulting, program, mastermind, 1:1, etc.
- **Link Analysis**: Calendly, Typeform, Linktree, course platforms
- **Post Content**: Client testimonials, program mentions, calls to action

## Installation

1. **Clone or download the project**
```bash
cd "Instagram Prospect Scraper"
```

2. **Install dependencies**
```bash
pip install -r requirements.txt
```

3. **Configure settings** (optional)
Edit `config.py` to adjust criteria, keywords, or proxy settings.

## Usage

### Basic Usage

**Scrape from hashtags (default mode):**
```bash
python main.py --mode hashtags
```

**Scrape specific usernames:**
```bash
python main.py --mode usernames --usernames user1 user2 user3
```

**Scrape custom hashtags:**
```bash
python main.py --mode hashtags --hashtags businesscoach lifecoach consultant
```

### Advanced Usage

**Use Selenium for more reliable scraping:**
```bash
python main.py --mode hashtags --use-selenium
```

**Mixed mode (usernames + hashtags):**
```bash
python main.py --mode mixed --usernames user1 user2 --hashtags businesscoach
```

**Custom output files:**
```bash
python main.py --output-csv my_prospects.csv --output-excel my_prospects.xlsx
```

### Command Line Options

- `--mode`: Scraping mode (`usernames`, `hashtags`, `mixed`)
- `--usernames`: List of specific usernames to scrape
- `--hashtags`: List of hashtags to scrape
- `--max-per-hashtag`: Maximum usernames per hashtag (default: 50)
- `--use-selenium`: Use Selenium for more reliable scraping
- `--output-csv`: Custom CSV filename
- `--output-excel`: Custom Excel filename

## Configuration

### Proxy Settings
The scraper uses the provided US proxies by default. You can modify proxy settings in `config.py`:

```python
PROXIES = [
    {
        'http': '*****************************************************',
        'https': '*****************************************************'
    },
    # Add more proxies...
]
```

### Criteria Customization
Adjust targeting criteria in `config.py`:

```python
FOLLOWER_COUNT_MIN = 1000
FOLLOWER_COUNT_MAX = 50000
MIN_ENGAGEMENT_RATE = 1.0
MIN_AVERAGE_COMMENTS = 5
```

### Keywords
Customize detection keywords:

```python
HIGH_TICKET_BIO_KEYWORDS = [
    'coaching', 'consultant', 'program', 'mastermind',
    # Add your keywords...
]
```

## Output

### CSV Export
- All prospects with detailed metrics
- Sortable by quality score
- Includes engagement data and qualification status

### Excel Export
- **All Prospects**: Complete dataset
- **Qualified Prospects**: Filtered qualified prospects only
- **Summary**: Analytics and statistics

### Data Fields
- Username and profile URL
- Follower/following counts
- Bio text and external link
- Engagement metrics (likes, comments, rate)
- High-ticket indicators
- Quality score and qualification status
- Timestamp

## Quality Scoring

Prospects are ranked using a comprehensive scoring system:

- **Engagement Score** (0-40 points): Based on engagement rate
- **High-Ticket Score** (0-30 points): Keywords and link indicators
- **Follower Score** (0-20 points): Optimal follower count range
- **Verification Bonus** (0-10 points): Verified accounts

## Rate Limiting

The scraper includes intelligent rate limiting:
- 2-second delay between requests
- Automatic proxy rotation on errors
- Retry logic with exponential backoff
- Respect for Instagram's rate limits

## Troubleshooting

### Common Issues

**1. No prospects found**
- Check if hashtags are relevant
- Verify proxy connectivity
- Ensure criteria aren't too restrictive

**2. Rate limiting errors**
- Increase `REQUEST_DELAY` in config.py
- Use `--use-selenium` for more reliable scraping
- Check proxy status

**3. Selenium issues**
- Ensure Chrome is installed
- Check ChromeDriver compatibility
- Verify proxy configuration

### Logging
Check `instagram_scraper.log` for detailed error information and debugging.

## Legal and Ethical Considerations

- **Public Data Only**: Only scrapes publicly available information
- **Rate Limiting**: Respects Instagram's servers with appropriate delays
- **No Authentication**: Does not require login or access private data
- **Compliance**: Use responsibly and in accordance with Instagram's Terms of Service

## File Structure

```
Instagram Prospect Scraper/
├── main.py                 # Main execution script
├── instagram_scraper.py    # Core scraping functionality
├── prospect_analyzer.py    # Analysis and qualification logic
├── data_exporter.py       # CSV/Excel export functionality
├── config.py              # Configuration and settings
├── requirements.txt       # Python dependencies
├── README.md             # This file
├── scraped_data/         # Output directory (created automatically)
└── instagram_scraper.log # Log file (created automatically)
```

## Support

For issues or questions:
1. Check the log file for error details
2. Verify configuration settings
3. Ensure all dependencies are installed
4. Test with a small sample first

## Version History

- **v1.0**: Initial release with core functionality
- Proxy support and rate limiting
- Dual scraping methods (requests + Selenium)
- Comprehensive analysis and export features
