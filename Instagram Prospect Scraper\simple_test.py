"""
Simple test to verify basic functionality
"""

import requests
import json
from datetime import datetime

# Test proxy connection
def test_proxy():
    proxies = {
        'http': '*****************************************************',
        'https': '*****************************************************'
    }
    
    try:
        response = requests.get("https://httpbin.org/ip", proxies=proxies, timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"Proxy working! IP: {data.get('origin')}")
            return True
        else:
            print(f"Proxy failed with status: {response.status_code}")
            return False
    except Exception as e:
        print(f"Proxy error: {e}")
        return False

# Test Instagram access (without login)
def test_instagram_access():
    proxies = {
        'http': '*****************************************************',
        'https': '*****************************************************'
    }
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    try:
        # Test accessing Instagram's main page
        response = requests.get("https://www.instagram.com/", proxies=proxies, headers=headers, timeout=15)
        print(f"Instagram access status: {response.status_code}")
        
        if response.status_code == 200:
            print("✓ Can access Instagram")
            return True
        else:
            print("✗ Cannot access Instagram")
            return False
            
    except Exception as e:
        print(f"Instagram access error: {e}")
        return False

# Simulate prospect analysis
def simulate_analysis():
    print("\nSimulating prospect analysis...")
    
    # Sample data that would come from Instagram
    sample_prospects = [
        {
            'username': 'sample_coach1',
            'followers': 15000,
            'bio': 'Business coach helping entrepreneurs scale. Book a call!',
            'external_url': 'https://calendly.com/sample-coach',
            'avg_likes': 150,
            'avg_comments': 12,
            'engagement_rate': 1.08
        },
        {
            'username': 'sample_coach2', 
            'followers': 8500,
            'bio': 'Life coach & transformation specialist. Apply for my program.',
            'external_url': 'https://typeform.com/apply',
            'avg_likes': 95,
            'avg_comments': 8,
            'engagement_rate': 1.21
        },
        {
            'username': 'sample_coach3',
            'followers': 25000,
            'bio': 'Marketing consultant. DM for strategy sessions.',
            'external_url': 'https://linktree.com/consultant',
            'avg_likes': 280,
            'avg_comments': 22,
            'engagement_rate': 1.21
        }
    ]
    
    qualified_count = 0
    
    for prospect in sample_prospects:
        # Check qualification criteria
        followers_ok = 1000 <= prospect['followers'] <= 50000
        engagement_ok = prospect['engagement_rate'] >= 1.0
        
        # Check for high-ticket keywords
        bio_lower = prospect['bio'].lower()
        high_ticket_keywords = ['coach', 'consultant', 'program', 'call', 'apply', 'strategy']
        has_keywords = any(keyword in bio_lower for keyword in high_ticket_keywords)
        
        # Check link
        qualifying_domains = ['calendly', 'typeform', 'linktree']
        has_qualifying_link = any(domain in prospect['external_url'] for domain in qualifying_domains)
        
        qualified = followers_ok and engagement_ok and (has_keywords or has_qualifying_link)
        
        if qualified:
            qualified_count += 1
        
        print(f"@{prospect['username']}:")
        print(f"  Followers: {prospect['followers']:,}")
        print(f"  Engagement: {prospect['engagement_rate']:.1f}%")
        print(f"  Qualified: {'✓' if qualified else '✗'}")
        print()
    
    print(f"Qualified prospects: {qualified_count}/{len(sample_prospects)}")
    return qualified_count

def main():
    print("Instagram Prospect Scraper - Simple Test")
    print("=" * 50)
    
    # Test 1: Proxy
    print("1. Testing proxy connection...")
    proxy_ok = test_proxy()
    
    # Test 2: Instagram access
    print("\n2. Testing Instagram access...")
    instagram_ok = test_instagram_access()
    
    # Test 3: Analysis simulation
    print("\n3. Testing analysis logic...")
    qualified_count = simulate_analysis()
    
    # Summary
    print("=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    print(f"Proxy connection: {'✓' if proxy_ok else '✗'}")
    print(f"Instagram access: {'✓' if instagram_ok else '✗'}")
    print(f"Analysis logic: ✓ (found {qualified_count} qualified prospects)")
    
    if proxy_ok and instagram_ok:
        print("\n✓ All systems ready! The scraper should work.")
        print("Next steps:")
        print("- Run: python main.py --mode hashtags")
        print("- Or: python run_us_coaches_test.py")
    else:
        print("\n⚠ Some issues detected:")
        if not proxy_ok:
            print("- Proxy connection failed")
        if not instagram_ok:
            print("- Instagram access blocked")
        print("- Try running with different settings or check network")

if __name__ == "__main__":
    main()
