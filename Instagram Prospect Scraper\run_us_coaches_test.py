"""
Test script to find US coaches using Instagram Prospect Scraper
"""

import sys
import os
import logging
import time
from datetime import datetime

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from instagram_scraper import InstagramScraper
    from prospect_analyzer import ProspectAnalyzer
    from data_exporter import DataExporter
    from config import TARGET_HASHTAGS
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure all required packages are installed: pip install requests beautifulsoup4 selenium pandas openpyxl fake-useragent webdriver-manager lxml")
    sys.exit(1)

def test_proxy_connection():
    """Test if proxy is working"""
    print("Testing proxy connection...")
    try:
        scraper = InstagramScraper(use_selenium=False)
        response = scraper._make_request("https://httpbin.org/ip")
        if response and response.status_code == 200:
            ip_info = response.json()
            print(f"✓ Proxy working - Current IP: {ip_info.get('origin', 'Unknown')}")
            scraper.close()
            return True
        else:
            print("✗ Proxy test failed")
            scraper.close()
            return False
    except Exception as e:
        print(f"✗ Proxy test error: {e}")
        return False

def find_us_coaches():
    """Find US-based coaches using the scraper"""
    print("\n" + "="*60)
    print("FINDING US-BASED COACHES")
    print("="*60)
    
    # Initialize components
    scraper = InstagramScraper(use_selenium=False)
    analyzer = ProspectAnalyzer()
    exporter = DataExporter()
    
    # US-focused hashtags for testing
    us_hashtags = [
        '#businesscoach',
        '#lifecoach', 
        '#entrepreneur',
        '#usacoach',
        '#americancoach'
    ]
    
    print(f"Searching hashtags: {', '.join(us_hashtags)}")
    print("This may take a few minutes...\n")
    
    all_prospects = []
    
    try:
        # Scrape usernames from hashtags
        for hashtag in us_hashtags:
            print(f"Scraping hashtag: {hashtag}")
            try:
                usernames = scraper.scrape_hashtag(hashtag, max_posts=10)  # Start small for testing
                print(f"  Found {len(usernames)} usernames")
                
                # Analyze each username
                for i, username in enumerate(usernames[:5], 1):  # Limit to 5 per hashtag for testing
                    try:
                        print(f"  Analyzing {i}/5: @{username}")
                        
                        # Get profile data
                        profile_data = scraper.get_profile_data(username)
                        if not profile_data:
                            print(f"    ✗ Could not get profile data")
                            continue
                        
                        # Quick check - skip if doesn't meet basic criteria
                        followers = profile_data.get('followers', 0)
                        if followers < 1000 or followers > 50000:
                            print(f"    ✗ Followers ({followers:,}) outside target range")
                            continue
                        
                        if profile_data.get('is_private', True):
                            print(f"    ✗ Private account")
                            continue
                        
                        # Get recent posts
                        posts_data = scraper.get_recent_posts(username, count=5)
                        
                        # Analyze prospect
                        analysis = analyzer.analyze_profile(profile_data, posts_data)
                        all_prospects.append(analysis)
                        
                        # Show results
                        qualified = analysis.get('qualified', False)
                        engagement = analysis.get('engagement_rate', 0)
                        high_ticket = analysis.get('high_ticket_qualified', False)
                        
                        print(f"    ✓ Analyzed - Followers: {followers:,}, "
                              f"Engagement: {engagement:.1f}%, "
                              f"High-ticket: {high_ticket}, "
                              f"Qualified: {qualified}")
                        
                        # Small delay between profiles
                        time.sleep(2)
                        
                    except Exception as e:
                        print(f"    ✗ Error analyzing {username}: {e}")
                        continue
                
                # Delay between hashtags
                time.sleep(3)
                
            except Exception as e:
                print(f"  ✗ Error with hashtag {hashtag}: {e}")
                continue
        
        # Process results
        if all_prospects:
            print(f"\n" + "="*60)
            print("ANALYSIS RESULTS")
            print("="*60)
            
            qualified_prospects = [p for p in all_prospects if p.get('qualified', False)]
            
            print(f"Total prospects analyzed: {len(all_prospects)}")
            print(f"Qualified prospects: {len(qualified_prospects)}")
            
            if qualified_prospects:
                # Rank prospects
                ranked_prospects = analyzer.rank_prospects(qualified_prospects)
                
                print(f"\nTop {min(5, len(ranked_prospects))} Qualified US Coaches:")
                print("-" * 50)
                
                for i, prospect in enumerate(ranked_prospects[:5], 1):
                    username = prospect.get('username', 'Unknown')
                    followers = prospect.get('follower_count', 0)
                    engagement = prospect.get('engagement_rate', 0)
                    score = prospect.get('quality_score', 0)
                    bio_keywords = prospect.get('bio_keywords_found', [])
                    
                    print(f"{i}. @{username}")
                    print(f"   Followers: {followers:,}")
                    print(f"   Engagement: {engagement:.1f}%")
                    print(f"   Quality Score: {score:.1f}")
                    print(f"   Keywords: {', '.join(bio_keywords[:3])}")
                    print(f"   Profile: https://instagram.com/{username}")
                    print()
                
                # Export results
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                csv_file = exporter.export_to_csv(all_prospects, f"us_coaches_test_{timestamp}.csv")
                excel_file = exporter.export_to_excel(all_prospects, f"us_coaches_test_{timestamp}.xlsx")
                
                print(f"Results exported to:")
                print(f"  CSV: {csv_file}")
                print(f"  Excel: {excel_file}")
                
            else:
                print("No qualified prospects found. Try:")
                print("- Adjusting criteria in config.py")
                print("- Using different hashtags")
                print("- Running with --use-selenium for better data")
        
        else:
            print("No prospects found. This could be due to:")
            print("- Instagram rate limiting")
            print("- Proxy issues")
            print("- Hashtag restrictions")
            print("- Network connectivity")
    
    except Exception as e:
        print(f"Error during scraping: {e}")
        logging.error(f"Scraping error: {e}", exc_info=True)
    
    finally:
        scraper.close()

def main():
    """Main function"""
    print("Instagram Prospect Scraper - US Coaches Test")
    print("=" * 60)
    
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('us_coaches_test.log'),
            logging.StreamHandler()
        ]
    )
    
    # Test proxy first
    if not test_proxy_connection():
        print("Proxy test failed. Continuing anyway...")
    
    # Find US coaches
    find_us_coaches()
    
    print("\n" + "="*60)
    print("Test completed! Check the log file and output files for details.")

if __name__ == "__main__":
    main()
