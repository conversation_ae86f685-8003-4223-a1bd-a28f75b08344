"""
Data Exporter for Instagram Prospect Scraper
Handles CSV and Excel export functionality
"""

import pandas as pd
import os
import logging
from typing import List, Dict
from datetime import datetime
from config import OUTPUT_DIR, CSV_FILENAME_PREFIX, EXCEL_FILENAME_PREFIX

class DataExporter:
    """Handles data export to CSV and Excel formats"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._ensure_output_directory()
    
    def _ensure_output_directory(self):
        """Create output directory if it doesn't exist"""
        if not os.path.exists(OUTPUT_DIR):
            os.makedirs(OUTPUT_DIR)
            self.logger.info(f"Created output directory: {OUTPUT_DIR}")
    
    def export_to_csv(self, prospects: List[Dict], filename: str = None) -> str:
        """
        Export prospects data to CSV file
        
        Args:
            prospects: List of prospect dictionaries
            filename: Optional custom filename
            
        Returns:
            Path to the created CSV file
        """
        if not filename:
            timestamp = int(datetime.now().timestamp())
            filename = f"{CSV_FILENAME_PREFIX}_{timestamp}.csv"
        
        filepath = os.path.join(OUTPUT_DIR, filename)
        
        try:
            # Convert to DataFrame
            df = self._prepare_dataframe(prospects)
            
            # Export to CSV
            df.to_csv(filepath, index=False, encoding='utf-8')
            
            self.logger.info(f"Exported {len(prospects)} prospects to CSV: {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"Error exporting to CSV: {e}")
            raise
    
    def export_to_excel(self, prospects: List[Dict], filename: str = None) -> str:
        """
        Export prospects data to Excel file with multiple sheets
        
        Args:
            prospects: List of prospect dictionaries
            filename: Optional custom filename
            
        Returns:
            Path to the created Excel file
        """
        if not filename:
            timestamp = int(datetime.now().timestamp())
            filename = f"{EXCEL_FILENAME_PREFIX}_{timestamp}.xlsx"
        
        filepath = os.path.join(OUTPUT_DIR, filename)
        
        try:
            # Prepare data
            df_all = self._prepare_dataframe(prospects)
            df_qualified = df_all[df_all['qualified'] == True].copy()
            df_summary = self._create_summary_dataframe(prospects)
            
            # Create Excel writer
            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                # All prospects sheet
                df_all.to_excel(writer, sheet_name='All Prospects', index=False)
                
                # Qualified prospects sheet
                df_qualified.to_excel(writer, sheet_name='Qualified Prospects', index=False)
                
                # Summary sheet
                df_summary.to_excel(writer, sheet_name='Summary', index=False)
                
                # Format sheets
                self._format_excel_sheets(writer, df_all, df_qualified, df_summary)
            
            self.logger.info(f"Exported {len(prospects)} prospects to Excel: {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"Error exporting to Excel: {e}")
            raise
    
    def _prepare_dataframe(self, prospects: List[Dict]) -> pd.DataFrame:
        """Prepare and clean data for export"""
        if not prospects:
            return pd.DataFrame()
        
        # Define column order and names
        columns = [
            'username', 'profile_url', 'full_name', 'follower_count', 'following_count',
            'bio_text', 'link_in_bio', 'post_count', 'avg_likes', 'avg_comments',
            'engagement_rate', 'posts_analyzed', 'high_ticket_qualified', 
            'bio_keywords_found', 'post_keywords_found', 'email_found',
            'link_analysis', 'quality_score', 'qualified', 'scraped_at'
        ]
        
        # Create DataFrame
        df = pd.DataFrame(prospects)
        
        # Ensure all columns exist
        for col in columns:
            if col not in df.columns:
                df[col] = None
        
        # Reorder columns
        df = df[columns]
        
        # Clean and format data
        df = self._clean_dataframe(df)
        
        return df
    
    def _clean_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean and format DataFrame for better readability"""
        # Convert lists to strings for better CSV/Excel compatibility
        list_columns = ['bio_keywords_found', 'post_keywords_found', 'recent_post_urls']
        for col in list_columns:
            if col in df.columns:
                df[col] = df[col].apply(lambda x: ', '.join(x) if isinstance(x, list) else str(x))
        
        # Convert dict columns to strings
        dict_columns = ['link_analysis']
        for col in dict_columns:
            if col in df.columns:
                df[col] = df[col].apply(lambda x: str(x) if isinstance(x, dict) else str(x))
        
        # Format numeric columns
        numeric_columns = ['follower_count', 'following_count', 'post_count', 'avg_likes', 'avg_comments']
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0).astype(int)
        
        # Format percentage columns
        percentage_columns = ['engagement_rate']
        for col in percentage_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0).round(2)
        
        # Clean text columns
        text_columns = ['bio_text', 'full_name']
        for col in text_columns:
            if col in df.columns:
                df[col] = df[col].astype(str).str.replace('\n', ' ').str.replace('\r', ' ')
        
        # Sort by quality score if available
        if 'quality_score' in df.columns:
            df = df.sort_values('quality_score', ascending=False)
        
        return df
    
    def _create_summary_dataframe(self, prospects: List[Dict]) -> pd.DataFrame:
        """Create summary statistics DataFrame"""
        if not prospects:
            return pd.DataFrame()
        
        total_prospects = len(prospects)
        qualified_prospects = len([p for p in prospects if p.get('qualified', False)])
        
        # Calculate averages for qualified prospects
        qualified = [p for p in prospects if p.get('qualified', False)]
        
        if qualified:
            avg_followers = sum(p.get('follower_count', 0) for p in qualified) / len(qualified)
            avg_engagement = sum(p.get('engagement_rate', 0) for p in qualified) / len(qualified)
            avg_quality_score = sum(p.get('quality_score', 0) for p in qualified) / len(qualified)
        else:
            avg_followers = 0
            avg_engagement = 0
            avg_quality_score = 0
        
        # Count by criteria
        follower_qualified = len([p for p in prospects if p.get('follower_qualified', False)])
        engagement_qualified = len([p for p in prospects if p.get('engagement_qualified', False)])
        high_ticket_qualified = len([p for p in prospects if p.get('high_ticket_qualified', False)])
        
        # Top keywords
        all_bio_keywords = []
        all_post_keywords = []
        for p in prospects:
            all_bio_keywords.extend(p.get('bio_keywords_found', []))
            all_post_keywords.extend(p.get('post_keywords_found', []))
        
        from collections import Counter
        top_bio_keywords = Counter(all_bio_keywords).most_common(5)
        top_post_keywords = Counter(all_post_keywords).most_common(5)
        
        summary_data = [
            ['Total Prospects Analyzed', total_prospects],
            ['Qualified Prospects', qualified_prospects],
            ['Qualification Rate (%)', round((qualified_prospects / total_prospects * 100) if total_prospects > 0 else 0, 2)],
            ['', ''],
            ['Criteria Breakdown:', ''],
            ['Follower Count Qualified', follower_qualified],
            ['Engagement Qualified', engagement_qualified],
            ['High-Ticket Qualified', high_ticket_qualified],
            ['', ''],
            ['Qualified Prospects Averages:', ''],
            ['Average Followers', round(avg_followers, 0)],
            ['Average Engagement Rate (%)', round(avg_engagement, 2)],
            ['Average Quality Score', round(avg_quality_score, 1)],
            ['', ''],
            ['Top Bio Keywords:', ''],
        ]
        
        # Add top keywords
        for keyword, count in top_bio_keywords:
            summary_data.append([f'  {keyword}', count])
        
        summary_data.append(['', ''])
        summary_data.append(['Top Post Keywords:', ''])
        
        for keyword, count in top_post_keywords:
            summary_data.append([f'  {keyword}', count])
        
        # Add timestamp
        summary_data.append(['', ''])
        summary_data.append(['Report Generated', datetime.now().strftime('%Y-%m-%d %H:%M:%S')])
        
        return pd.DataFrame(summary_data, columns=['Metric', 'Value'])
    
    def _format_excel_sheets(self, writer, df_all, df_qualified, df_summary):
        """Format Excel sheets for better readability"""
        try:
            from openpyxl.styles import Font, PatternFill, Alignment
            from openpyxl.utils.dataframe import dataframe_to_rows
            
            # Get workbook and worksheets
            workbook = writer.book
            
            # Format headers
            header_font = Font(bold=True, color='FFFFFF')
            header_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
            
            for sheet_name in workbook.sheetnames:
                worksheet = workbook[sheet_name]
                
                # Format header row
                for cell in worksheet[1]:
                    cell.font = header_font
                    cell.fill = header_fill
                    cell.alignment = Alignment(horizontal='center')
                
                # Auto-adjust column widths
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    
                    adjusted_width = min(max_length + 2, 50)  # Cap at 50 characters
                    worksheet.column_dimensions[column_letter].width = adjusted_width
            
        except ImportError:
            # openpyxl styling not available, skip formatting
            self.logger.warning("openpyxl styling not available, skipping Excel formatting")
        except Exception as e:
            self.logger.warning(f"Error formatting Excel sheets: {e}")
    
    def get_export_summary(self, filepath: str, prospects: List[Dict]) -> str:
        """Generate a summary of the export"""
        total = len(prospects)
        qualified = len([p for p in prospects if p.get('qualified', False)])
        
        summary = f"""
Export Summary:
- File: {filepath}
- Total prospects: {total}
- Qualified prospects: {qualified}
- Qualification rate: {(qualified/total*100):.1f}% if total > 0 else 0%
- Export time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        """
        
        return summary.strip()
