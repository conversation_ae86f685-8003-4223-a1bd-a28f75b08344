"""
Prospect Analyzer for Instagram Scraper
Analyzes profiles for engagement and high-ticket offer indicators
"""

import re
import logging
from typing import Dict, List, Tuple, Optional
from urllib.parse import urlparse
from config import *

class ProspectAnalyzer:
    """Analyzes Instagram profiles for prospect qualification"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def analyze_profile(self, profile_data: Dict, posts_data: List[Dict]) -> Dict:
        """
        Comprehensive analysis of a profile for prospect qualification
        
        Args:
            profile_data: Profile information from Instagram
            posts_data: Recent posts data for engagement analysis
            
        Returns:
            Analysis results with qualification status and metrics
        """
        analysis = {
            'username': profile_data.get('username', ''),
            'profile_url': f"{INSTAGRAM_BASE_URL}/{profile_data.get('username', '')}",
            'full_name': profile_data.get('full_name', ''),
            'follower_count': profile_data.get('followers', 0),
            'following_count': profile_data.get('following', 0),
            'bio_text': profile_data.get('bio', ''),
            'link_in_bio': profile_data.get('external_url', ''),
            'post_count': profile_data.get('posts', 0),
            'is_private': profile_data.get('is_private', False),
            'is_verified': profile_data.get('is_verified', False),
        }
        
        # Check follower count criteria
        follower_qualified = self._check_follower_criteria(analysis['follower_count'])
        analysis['follower_qualified'] = follower_qualified
        
        # Analyze engagement
        engagement_analysis = self._analyze_engagement(posts_data, analysis['follower_count'])
        analysis.update(engagement_analysis)
        
        # Analyze high-ticket offer indicators
        high_ticket_analysis = self._analyze_high_ticket_indicators(
            analysis['bio_text'], 
            analysis['link_in_bio'], 
            posts_data
        )
        analysis.update(high_ticket_analysis)
        
        # Determine overall qualification
        analysis['qualified'] = self._determine_qualification(analysis)
        
        # Add timestamp
        import datetime
        analysis['scraped_at'] = datetime.datetime.now().isoformat()
        
        return analysis
    
    def _check_follower_criteria(self, follower_count: int) -> bool:
        """Check if follower count meets criteria"""
        return FOLLOWER_COUNT_MIN <= follower_count <= FOLLOWER_COUNT_MAX
    
    def _analyze_engagement(self, posts_data: List[Dict], follower_count: int) -> Dict:
        """Analyze engagement metrics from recent posts"""
        if not posts_data or follower_count == 0:
            return {
                'avg_likes': 0,
                'avg_comments': 0,
                'engagement_rate': 0.0,
                'engagement_qualified': False,
                'posts_analyzed': 0
            }
        
        total_likes = sum(post.get('likes', 0) for post in posts_data)
        total_comments = sum(post.get('comments', 0) for post in posts_data)
        posts_count = len(posts_data)
        
        avg_likes = total_likes / posts_count if posts_count > 0 else 0
        avg_comments = total_comments / posts_count if posts_count > 0 else 0
        
        # Calculate engagement rate: (avg likes + avg comments) / followers * 100
        engagement_rate = ((avg_likes + avg_comments) / follower_count * 100) if follower_count > 0 else 0
        
        # Check engagement criteria
        likes_percentage = (avg_likes / follower_count * 100) if follower_count > 0 else 0
        
        engagement_qualified = (
            engagement_rate >= MIN_ENGAGEMENT_RATE and
            likes_percentage >= MIN_AVERAGE_LIKES_PERCENTAGE and
            avg_comments >= MIN_AVERAGE_COMMENTS
        )
        
        return {
            'avg_likes': round(avg_likes, 2),
            'avg_comments': round(avg_comments, 2),
            'engagement_rate': round(engagement_rate, 2),
            'engagement_qualified': engagement_qualified,
            'posts_analyzed': posts_count,
            'recent_post_urls': [post.get('url', '') for post in posts_data[:3]]  # Last 3 posts
        }
    
    def _analyze_high_ticket_indicators(self, bio: str, external_url: str, posts_data: List[Dict]) -> Dict:
        """Analyze indicators of high-ticket offers"""
        bio_keywords_found = []
        post_keywords_found = []
        
        # Analyze bio for keywords
        bio_lower = bio.lower()
        for keyword in HIGH_TICKET_BIO_KEYWORDS:
            if keyword.lower() in bio_lower:
                bio_keywords_found.append(keyword)
        
        # Analyze posts for keywords
        post_texts = []
        for post in posts_data:
            caption = post.get('caption', '')
            if caption:
                post_texts.append(caption.lower())
        
        combined_post_text = ' '.join(post_texts)
        for keyword in HIGH_TICKET_POST_KEYWORDS:
            if keyword.lower() in combined_post_text:
                post_keywords_found.append(keyword)
        
        # Analyze external URL
        link_analysis = self._analyze_external_link(external_url)
        
        # Extract email if present in bio
        email = self._extract_email_from_bio(bio)
        
        # Determine high-ticket qualification
        has_bio_indicators = len(bio_keywords_found) > 0
        has_post_indicators = len(post_keywords_found) > 0
        has_qualifying_link = link_analysis['is_high_ticket_domain']
        
        high_ticket_qualified = has_bio_indicators or has_qualifying_link
        
        return {
            'high_ticket_qualified': high_ticket_qualified,
            'bio_keywords_found': bio_keywords_found,
            'post_keywords_found': post_keywords_found,
            'link_analysis': link_analysis,
            'email_found': email,
            'high_ticket_score': len(bio_keywords_found) + len(post_keywords_found) + (1 if has_qualifying_link else 0)
        }
    
    def _analyze_external_link(self, url: str) -> Dict:
        """Analyze external URL for high-ticket indicators"""
        if not url:
            return {
                'domain': '',
                'is_high_ticket_domain': False,
                'link_type': 'none'
            }
        
        try:
            parsed_url = urlparse(url)
            domain = parsed_url.netloc.lower()
            
            # Check against known high-ticket domains
            is_high_ticket = any(ht_domain in domain for ht_domain in HIGH_TICKET_LINK_DOMAINS)
            
            # Determine link type
            link_type = 'other'
            if 'calendly' in domain or 'acuity' in domain:
                link_type = 'booking'
            elif 'typeform' in domain or 'jotform' in domain:
                link_type = 'application'
            elif 'linktree' in domain or 'beacons' in domain or 'bio.link' in domain:
                link_type = 'link_aggregator'
            elif any(funnel in domain for funnel in ['clickfunnels', 'leadpages', 'unbounce']):
                link_type = 'landing_page'
            elif any(course in domain for course in ['kajabi', 'teachable', 'podia', 'thrivecart']):
                link_type = 'course_platform'
            
            return {
                'domain': domain,
                'is_high_ticket_domain': is_high_ticket,
                'link_type': link_type,
                'full_url': url
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing URL {url}: {e}")
            return {
                'domain': '',
                'is_high_ticket_domain': False,
                'link_type': 'error'
            }
    
    def _extract_email_from_bio(self, bio: str) -> str:
        """Extract email address from bio if present"""
        if not bio:
            return ''
        
        # Simple email regex
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        matches = re.findall(email_pattern, bio)
        
        return matches[0] if matches else ''
    
    def _determine_qualification(self, analysis: Dict) -> bool:
        """Determine overall qualification based on all criteria"""
        return (
            analysis.get('follower_qualified', False) and
            analysis.get('engagement_qualified', False) and
            analysis.get('high_ticket_qualified', False) and
            not analysis.get('is_private', True)  # Exclude private accounts
        )
    
    def filter_qualified_prospects(self, prospects: List[Dict]) -> List[Dict]:
        """Filter list of prospects to only qualified ones"""
        return [prospect for prospect in prospects if prospect.get('qualified', False)]
    
    def rank_prospects(self, prospects: List[Dict]) -> List[Dict]:
        """Rank prospects by quality score"""
        def calculate_score(prospect):
            score = 0
            
            # Engagement score (0-40 points)
            engagement_rate = prospect.get('engagement_rate', 0)
            score += min(engagement_rate * 2, 40)  # Cap at 40 points
            
            # High-ticket score (0-30 points)
            high_ticket_score = prospect.get('high_ticket_score', 0)
            score += min(high_ticket_score * 10, 30)  # Cap at 30 points
            
            # Follower count score (0-20 points) - sweet spot around 10-20k
            followers = prospect.get('follower_count', 0)
            if 10000 <= followers <= 20000:
                score += 20
            elif 5000 <= followers <= 30000:
                score += 15
            elif 1000 <= followers <= 50000:
                score += 10
            
            # Verification bonus (0-10 points)
            if prospect.get('is_verified', False):
                score += 10
            
            return score
        
        # Add score to each prospect and sort
        for prospect in prospects:
            prospect['quality_score'] = calculate_score(prospect)
        
        return sorted(prospects, key=lambda x: x.get('quality_score', 0), reverse=True)
