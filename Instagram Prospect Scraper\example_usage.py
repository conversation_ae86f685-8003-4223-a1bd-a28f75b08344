"""
Example usage of Instagram Prospect Scraper
"""

from main import InstagramProspectScraper
import logging

# Set up logging to see what's happening
logging.basicConfig(level=logging.INFO)

def example_username_scraping():
    """Example: Scrape specific usernames"""
    print("Example 1: Scraping specific usernames")
    print("=" * 40)
    
    # Initialize scraper
    scraper = InstagramProspectScraper(use_selenium=False)
    
    # List of usernames to scrape (replace with real usernames)
    usernames = [
        'garyvee',  # Example - replace with actual coaching/consulting accounts
        'tonyrobbins',
        'brendonburchard'
    ]
    
    try:
        # Scrape prospects
        prospects = scraper.scrape_from_usernames(usernames)
        
        # Run analysis and export
        if prospects:
            summary = scraper.run_full_analysis(prospects)
            print(f"Found {summary['qualified_prospects']} qualified prospects")
            print(f"Results saved to: {summary['csv_file']}")
        else:
            print("No prospects found")
            
    except Exception as e:
        print(f"Error: {e}")
    finally:
        scraper.close()

def example_hashtag_scraping():
    """Example: Scrape from hashtags"""
    print("\nExample 2: Scraping from hashtags")
    print("=" * 40)
    
    # Initialize scraper
    scraper = InstagramProspectScraper(use_selenium=False)
    
    # Hashtags to scrape
    hashtags = [
        '#businesscoach',
        '#lifecoach',
        '#consultant'
    ]
    
    try:
        # Scrape prospects from hashtags
        prospects = scraper.scrape_from_hashtags(hashtags, max_per_hashtag=20)
        
        # Run analysis and export
        if prospects:
            summary = scraper.run_full_analysis(prospects)
            print(f"Found {summary['qualified_prospects']} qualified prospects")
            print(f"Results saved to: {summary['csv_file']}")
            
            # Show top prospects
            if summary['top_prospects']:
                print("\nTop 3 prospects:")
                for i, prospect in enumerate(summary['top_prospects'][:3], 1):
                    print(f"{i}. @{prospect['username']} - "
                          f"Score: {prospect.get('quality_score', 0):.1f}")
        else:
            print("No prospects found")
            
    except Exception as e:
        print(f"Error: {e}")
    finally:
        scraper.close()

def example_custom_analysis():
    """Example: Custom analysis of a single prospect"""
    print("\nExample 3: Custom analysis")
    print("=" * 40)
    
    from instagram_scraper import InstagramScraper
    from prospect_analyzer import ProspectAnalyzer
    
    scraper = InstagramScraper()
    analyzer = ProspectAnalyzer()
    
    username = "garyvee"  # Replace with actual username
    
    try:
        # Get profile data
        profile_data = scraper.get_profile_data(username)
        if not profile_data:
            print(f"Could not get data for {username}")
            return
        
        # Get recent posts
        posts_data = scraper.get_recent_posts(username, count=5)
        
        # Analyze
        analysis = analyzer.analyze_profile(profile_data, posts_data)
        
        # Print detailed analysis
        print(f"Analysis for @{username}:")
        print(f"  Followers: {analysis['follower_count']:,}")
        print(f"  Engagement Rate: {analysis['engagement_rate']:.2f}%")
        print(f"  High-ticket qualified: {analysis['high_ticket_qualified']}")
        print(f"  Bio keywords: {analysis['bio_keywords_found']}")
        print(f"  Quality score: {analysis.get('quality_score', 0):.1f}")
        print(f"  Overall qualified: {analysis['qualified']}")
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        scraper.close()

if __name__ == "__main__":
    print("Instagram Prospect Scraper - Example Usage")
    print("=" * 50)
    
    # Run examples
    example_username_scraping()
    example_hashtag_scraping()
    example_custom_analysis()
    
    print("\n" + "=" * 50)
    print("Examples completed!")
    print("Check the 'scraped_data' folder for output files.")
