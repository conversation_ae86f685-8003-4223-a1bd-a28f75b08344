"""
Canadian Coaches & Consultants Test Script
Meets exact requirements for finding 100-150 qualified Canadian prospects
"""

import sys
import os
import logging
import time
from datetime import datetime
from typing import List, Dict

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from instagram_scraper import InstagramScraper
    from prospect_analyzer import ProspectAnalyzer
    from data_exporter import DataExporter
    from config import TARGET_HASHTAGS
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure all required packages are installed")
    sys.exit(1)

class CanadianCoachesScraperTest:
    """Specialized scraper for Canadian coaches and consultants"""
    
    def __init__(self):
        self.scraper = InstagramScraper(use_selenium=False)
        self.analyzer = ProspectAnalyzer()
        self.exporter = DataExporter()
        self.logger = logging.getLogger(__name__)
        
        # Canadian-specific hashtags for the test
        self.canadian_hashtags = [
            '#canadiancoach',
            '#torontobusinesscoach',
            '#vancouverlifecoach',
            '#canadianconsultant',
            '#businesscoachcanada',
            '#lifecoachcanada',
            '#consultantcanada',
            '#canadianentrepreneur',
            '#torontocoach',
            '#vancouvercoach',
            '#calgarycoach',
            '#montrealcoach',
            '#canadianbusiness',
            '#mindsetcoachcanada',
            '#businesscoachtoronto',
            '#lifecoachvancouver'
        ]
        
        # Target: Find 100-150 qualified prospects
        self.target_qualified_count = 150
        self.max_profiles_per_hashtag = 50
        
    def meets_exact_criteria(self, profile_data: Dict, posts_data: List[Dict]) -> Dict:
        """
        Check if profile meets EXACT requirements:
        1. Follower count: 1,000 - 50,000
        2. Engagement: 1%+ average likes on last 5-10 posts
        3. High-ticket offer indicators in bio or link
        4. Canadian location indicators
        """
        
        # Basic profile info
        username = profile_data.get('username', '')
        followers = profile_data.get('followers', 0)
        bio = profile_data.get('bio', '').lower()
        external_url = profile_data.get('external_url', '')
        is_private = profile_data.get('is_private', True)
        
        # Criteria 1: Follower count (1k-50k)
        follower_qualified = 1000 <= followers <= 50000
        
        # Criteria 2: Engagement rate (1%+ average likes)
        if not posts_data or followers == 0:
            engagement_qualified = False
            avg_likes = 0
            engagement_rate = 0
        else:
            total_likes = sum(post.get('likes', 0) for post in posts_data)
            avg_likes = total_likes / len(posts_data)
            engagement_rate = (avg_likes / followers) * 100
            engagement_qualified = engagement_rate >= 1.0
        
        # Criteria 3: High-ticket offer indicators
        high_ticket_bio_keywords = [
            'coaching', 'consultant', 'consulting', 'program', 'mastermind',
            '1:1', 'one-on-one', 'apply now', 'book a call', 'signature course',
            'high-ticket', 'premium', 'exclusive', 'transformation', 'breakthrough',
            'strategy session', 'discovery call', 'application', 'enrollment',
            'mentorship', 'accelerator', 'intensive', 'blueprint'
        ]
        
        # Check bio for high-ticket keywords
        bio_keywords_found = [kw for kw in high_ticket_bio_keywords if kw in bio]
        has_bio_indicators = len(bio_keywords_found) > 0
        
        # Check link for high-ticket domains
        high_ticket_domains = [
            'calendly.com', 'acuity', 'typeform.com', 'jotform.com',
            'linktree.com', 'beacons.ai', 'linktr.ee', 'bio.link',
            'clickfunnels.com', 'leadpages.com', 'kajabi.com', 'teachable.com'
        ]
        
        has_qualifying_link = any(domain in external_url.lower() for domain in high_ticket_domains)
        high_ticket_qualified = has_bio_indicators or has_qualifying_link
        
        # Criteria 4: Canadian indicators
        canadian_keywords = [
            'canada', 'canadian', 'toronto', 'vancouver', 'calgary', 'montreal',
            'ottawa', 'edmonton', 'winnipeg', 'quebec', 'ontario', 'british columbia',
            'alberta', 'bc', 'on', 'ab', 'qc'
        ]
        
        canadian_indicators = [kw for kw in canadian_keywords if kw in bio]
        is_canadian = len(canadian_indicators) > 0
        
        # Overall qualification
        qualified = (
            follower_qualified and 
            engagement_qualified and 
            high_ticket_qualified and 
            is_canadian and 
            not is_private
        )
        
        # Determine high-ticket reasoning
        high_ticket_reasoning = []
        if bio_keywords_found:
            high_ticket_reasoning.append(f"Bio contains: {', '.join(bio_keywords_found[:3])}")
        if has_qualifying_link:
            domain = next((d for d in high_ticket_domains if d in external_url.lower()), 'qualifying domain')
            high_ticket_reasoning.append(f"Link-in-bio to {domain}")
        
        return {
            'username': username,
            'profile_url': f"https://instagram.com/{username}",
            'full_name': profile_data.get('full_name', ''),
            'follower_count': followers,
            'bio_text': profile_data.get('bio', ''),
            'link_in_bio_url': external_url,
            'average_likes': round(avg_likes, 1),
            'engagement_rate': round(engagement_rate, 2),
            'high_ticket_offer_found': high_ticket_qualified,
            'high_ticket_reasoning': '; '.join(high_ticket_reasoning),
            'timestamp': datetime.now().isoformat(),
            'qualified': qualified,
            'canadian_indicators': canadian_indicators,
            'follower_qualified': follower_qualified,
            'engagement_qualified': engagement_qualified,
            'is_private': is_private
        }
    
    def run_canadian_test(self) -> List[Dict]:
        """Run the Canadian coaches test to find 100-150 qualified prospects"""
        
        print("🇨🇦 CANADIAN COACHES & CONSULTANTS TEST")
        print("=" * 60)
        print(f"Target: {self.target_qualified_count} qualified prospects")
        print(f"Criteria: 1K-50K followers, 1%+ engagement, high-ticket offers, Canadian")
        print()
        
        all_prospects = []
        qualified_prospects = []
        processed_usernames = set()
        
        for hashtag_index, hashtag in enumerate(self.canadian_hashtags, 1):
            if len(qualified_prospects) >= self.target_qualified_count:
                print(f"✅ Target reached! Found {len(qualified_prospects)} qualified prospects")
                break
                
            print(f"[{hashtag_index}/{len(self.canadian_hashtags)}] Scraping: {hashtag}")
            
            try:
                # Get usernames from hashtag
                usernames = self.scraper.scrape_hashtag(hashtag, self.max_profiles_per_hashtag)
                print(f"  Found {len(usernames)} usernames")
                
                # Process each username
                for i, username in enumerate(usernames, 1):
                    if username in processed_usernames:
                        continue
                    
                    processed_usernames.add(username)
                    
                    try:
                        print(f"  [{i}/{len(usernames)}] Analyzing @{username}")
                        
                        # Get profile data
                        profile_data = self.scraper.get_profile_data(username)
                        if not profile_data:
                            print(f"    ✗ Could not get profile data")
                            continue
                        
                        # Quick pre-filter
                        followers = profile_data.get('followers', 0)
                        if followers < 1000 or followers > 50000:
                            print(f"    ✗ Followers ({followers:,}) outside range")
                            continue
                        
                        if profile_data.get('is_private', True):
                            print(f"    ✗ Private account")
                            continue
                        
                        # Get recent posts for engagement analysis
                        posts_data = self.scraper.get_recent_posts(username, count=8)
                        
                        # Apply exact criteria
                        analysis = self.meets_exact_criteria(profile_data, posts_data)
                        all_prospects.append(analysis)
                        
                        if analysis['qualified']:
                            qualified_prospects.append(analysis)
                            print(f"    ✅ QUALIFIED ({len(qualified_prospects)}/{self.target_qualified_count})")
                            print(f"       Followers: {followers:,}, Engagement: {analysis['engagement_rate']:.1f}%")
                            print(f"       Reasoning: {analysis['high_ticket_reasoning']}")
                        else:
                            reasons = []
                            if not analysis['follower_qualified']:
                                reasons.append("followers")
                            if not analysis['engagement_qualified']:
                                reasons.append("engagement")
                            if not analysis['high_ticket_offer_found']:
                                reasons.append("no high-ticket")
                            if not analysis['canadian_indicators']:
                                reasons.append("not Canadian")
                            print(f"    ✗ Not qualified: {', '.join(reasons)}")
                        
                        # Rate limiting
                        time.sleep(2)
                        
                        # Check if we've reached target
                        if len(qualified_prospects) >= self.target_qualified_count:
                            print(f"    🎯 Target reached!")
                            break
                            
                    except Exception as e:
                        print(f"    ✗ Error analyzing {username}: {e}")
                        continue
                
                # Delay between hashtags
                time.sleep(5)
                
            except Exception as e:
                print(f"  ✗ Error with hashtag {hashtag}: {e}")
                continue
        
        return qualified_prospects, all_prospects
    
    def export_results(self, qualified_prospects: List[Dict], all_prospects: List[Dict]) -> Dict:
        """Export results in the exact format required"""
        
        if not qualified_prospects:
            print("❌ No qualified prospects found!")
            return {}
        
        # Prepare data for export (exact columns required)
        export_data = []
        for prospect in qualified_prospects:
            export_data.append({
                'Username': prospect['username'],
                'Profile URL': prospect['profile_url'],
                'Full Name': prospect['full_name'],
                'Follower Count': prospect['follower_count'],
                'Bio Text': prospect['bio_text'],
                'Link in Bio URL': prospect['link_in_bio_url'],
                'Average Likes': prospect['average_likes'],
                'Engagement Rate (%)': prospect['engagement_rate'],
                'High Ticket Offer Found': prospect['high_ticket_offer_found'],
                'High Ticket Reasoning': prospect['high_ticket_reasoning'],
                'Timestamp': prospect['timestamp']
            })
        
        # Export to CSV and Excel
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        csv_filename = f"canadian_coaches_test_{timestamp}.csv"
        excel_filename = f"canadian_coaches_test_{timestamp}.xlsx"
        
        import pandas as pd
        df = pd.DataFrame(export_data)
        
        # Export CSV
        csv_path = os.path.join('scraped_data', csv_filename)
        df.to_csv(csv_path, index=False)
        
        # Export Excel with summary
        excel_path = os.path.join('scraped_data', excel_filename)
        with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Qualified Prospects', index=False)
            
            # Summary sheet
            summary_data = [
                ['Total Profiles Analyzed', len(all_prospects)],
                ['Qualified Prospects Found', len(qualified_prospects)],
                ['Qualification Rate (%)', round(len(qualified_prospects)/len(all_prospects)*100, 1) if all_prospects else 0],
                ['Average Followers (Qualified)', round(df['Follower Count'].mean(), 0) if not df.empty else 0],
                ['Average Engagement Rate (%)', round(df['Engagement Rate (%)'].mean(), 2) if not df.empty else 0],
                ['Test Completion', 'SUCCESS' if len(qualified_prospects) >= 100 else 'PARTIAL'],
                ['Timestamp', datetime.now().strftime('%Y-%m-%d %H:%M:%S')]
            ]
            summary_df = pd.DataFrame(summary_data, columns=['Metric', 'Value'])
            summary_df.to_excel(writer, sheet_name='Test Summary', index=False)
        
        return {
            'csv_file': csv_path,
            'excel_file': excel_path,
            'qualified_count': len(qualified_prospects),
            'total_analyzed': len(all_prospects)
        }
    
    def close(self):
        """Clean up resources"""
        self.scraper.close()

def main():
    """Main test function"""
    
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('canadian_coaches_test.log'),
            logging.StreamHandler()
        ]
    )
    
    test_scraper = CanadianCoachesScraperTest()
    
    try:
        # Run the test
        qualified_prospects, all_prospects = test_scraper.run_canadian_test()
        
        # Export results
        if qualified_prospects:
            results = test_scraper.export_results(qualified_prospects, all_prospects)
            
            print("\n" + "=" * 60)
            print("🎯 CANADIAN COACHES TEST RESULTS")
            print("=" * 60)
            print(f"✅ Qualified prospects found: {results['qualified_count']}")
            print(f"📊 Total profiles analyzed: {results['total_analyzed']}")
            print(f"📈 Success rate: {results['qualified_count']/results['total_analyzed']*100:.1f}%")
            print(f"📁 CSV file: {results['csv_file']}")
            print(f"📁 Excel file: {results['excel_file']}")
            
            if results['qualified_count'] >= 100:
                print("🏆 TEST PASSED: Found 100+ qualified Canadian coaches!")
            else:
                print(f"⚠️  TEST PARTIAL: Found {results['qualified_count']}/100+ prospects")
        else:
            print("❌ TEST FAILED: No qualified prospects found")
    
    except Exception as e:
        print(f"❌ Test error: {e}")
        logging.error(f"Test failed: {e}", exc_info=True)
    
    finally:
        test_scraper.close()

if __name__ == "__main__":
    main()
