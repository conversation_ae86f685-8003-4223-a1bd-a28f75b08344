"""
Instagram Scraper for Prospect Identification
"""

import requests
import time
import json
import re
import logging
from typing import List, Dict, Optional, Tuple
from urllib.parse import urlparse, parse_qs
from bs4 import BeautifulSoup
from fake_useragent import UserAgent
import random
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager

from config import *

class InstagramScraper:
    """Main Instagram scraper class for prospect identification"""

    def __init__(self, use_selenium: bool = False):
        self.session = requests.Session()
        self.ua = UserAgent()
        self.current_proxy_index = 0
        self.use_selenium = use_selenium
        self.driver = None

        # Setup logging
        logging.basicConfig(
            level=getattr(logging, LOG_LEVEL),
            format=LOG_FORMAT,
            handlers=[
                logging.FileHandler(LOG_FILE),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

        # Setup session
        self._setup_session()

        # Setup Selenium if needed
        if use_selenium:
            self._setup_selenium()

    def _setup_session(self):
        """Setup requests session with proxy and headers"""
        proxy = PROXIES[self.current_proxy_index]
        self.session.proxies.update(proxy)

        headers = {
            'User-Agent': self.ua.random,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        self.session.headers.update(headers)

        self.logger.info(f"Session setup with proxy: {proxy['http']}")

    def _setup_selenium(self):
        """Setup Selenium WebDriver with proxy"""
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument(f'--user-agent={self.ua.random}')

        # Add proxy
        proxy = PROXIES[self.current_proxy_index]
        proxy_host = proxy['http'].split('@')[1].split(':')[0]
        proxy_port = proxy['http'].split('@')[1].split(':')[1]
        proxy_user = proxy['http'].split('://')[1].split(':')[0]
        proxy_pass = proxy['http'].split('://')[1].split('@')[0].split(':')[1]

        chrome_options.add_argument(f'--proxy-server=http://{proxy_host}:{proxy_port}')

        try:
            self.driver = webdriver.Chrome(
                service=webdriver.chrome.service.Service(ChromeDriverManager().install()),
                options=chrome_options
            )
            self.logger.info("Selenium WebDriver setup successful")
        except Exception as e:
            self.logger.error(f"Failed to setup Selenium: {e}")
            self.use_selenium = False

    def _rotate_proxy(self):
        """Rotate to next proxy"""
        self.current_proxy_index = (self.current_proxy_index + 1) % len(PROXIES)
        self._setup_session()
        if self.use_selenium and self.driver:
            self.driver.quit()
            self._setup_selenium()

    def _make_request(self, url: str, retries: int = MAX_RETRIES) -> Optional[requests.Response]:
        """Make HTTP request with retry logic"""
        for attempt in range(retries):
            try:
                response = self.session.get(url, timeout=TIMEOUT)
                if response.status_code == 200:
                    return response
                elif response.status_code == 429:  # Rate limited
                    self.logger.warning("Rate limited, waiting...")
                    time.sleep(60)
                    continue
                elif response.status_code in [403, 404]:
                    self.logger.warning(f"Access denied or not found: {url}")
                    return None
                else:
                    self.logger.warning(f"Unexpected status code {response.status_code} for {url}")

            except requests.exceptions.RequestException as e:
                self.logger.error(f"Request failed (attempt {attempt + 1}): {e}")
                if attempt < retries - 1:
                    time.sleep(REQUEST_DELAY * (attempt + 1))
                    if attempt == retries // 2:  # Rotate proxy halfway through retries
                        self._rotate_proxy()

        return None

    def get_profile_data(self, username: str) -> Optional[Dict]:
        """Extract profile data from Instagram username"""
        url = f"{INSTAGRAM_BASE_URL}/{username}/"

        if self.use_selenium:
            return self._get_profile_data_selenium(url, username)
        else:
            return self._get_profile_data_requests(url, username)

    def _get_profile_data_requests(self, url: str, username: str) -> Optional[Dict]:
        """Get profile data using requests"""
        response = self._make_request(url)
        if not response:
            return None

        try:
            soup = BeautifulSoup(response.text, 'html.parser')

            # Extract JSON data from script tags
            script_tags = soup.find_all('script', type='text/javascript')
            profile_data = None

            for script in script_tags:
                if script.string and 'window._sharedData' in script.string:
                    # Extract JSON from window._sharedData
                    json_text = script.string
                    start = json_text.find('window._sharedData = ') + len('window._sharedData = ')
                    end = json_text.find(';</script>')
                    if end == -1:
                        end = json_text.find(';', start)

                    try:
                        shared_data = json.loads(json_text[start:end])
                        entry_data = shared_data.get('entry_data', {})
                        profile_page = entry_data.get('ProfilePage', [])
                        if profile_page:
                            profile_data = profile_page[0]['graphql']['user']
                            break
                    except (json.JSONDecodeError, KeyError, IndexError):
                        continue

            if not profile_data:
                # Try alternative method - look for JSON-LD or meta tags
                return self._extract_profile_from_meta(soup, username)

            return self._parse_profile_data(profile_data, username)

        except Exception as e:
            self.logger.error(f"Error parsing profile data for {username}: {e}")
            return None

    def _get_profile_data_selenium(self, url: str, username: str) -> Optional[Dict]:
        """Get profile data using Selenium"""
        if not self.driver:
            return None

        try:
            self.driver.get(url)
            time.sleep(3)

            # Wait for profile to load
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "article"))
            )

            # Extract profile information
            profile_data = {}

            # Get follower count
            try:
                followers_element = self.driver.find_element(
                    By.XPATH, "//a[contains(@href, '/followers/')]/span"
                )
                profile_data['followers'] = self._parse_count(followers_element.get_attribute('title') or followers_element.text)
            except NoSuchElementException:
                profile_data['followers'] = 0

            # Get following count
            try:
                following_element = self.driver.find_element(
                    By.XPATH, "//a[contains(@href, '/following/')]/span"
                )
                profile_data['following'] = self._parse_count(following_element.get_attribute('title') or following_element.text)
            except NoSuchElementException:
                profile_data['following'] = 0

            # Get post count
            try:
                posts_element = self.driver.find_element(
                    By.XPATH, "//div[contains(text(), 'posts')]/span"
                )
                profile_data['posts'] = self._parse_count(posts_element.text)
            except NoSuchElementException:
                profile_data['posts'] = 0

            # Get bio
            try:
                bio_element = self.driver.find_element(
                    By.XPATH, "//div[@data-testid='user-bio']"
                )
                profile_data['bio'] = bio_element.text
            except NoSuchElementException:
                profile_data['bio'] = ""

            # Get external URL
            try:
                url_element = self.driver.find_element(
                    By.XPATH, "//a[contains(@class, 'x1i10hfl') and contains(@href, 'http')]"
                )
                profile_data['external_url'] = url_element.get_attribute('href')
            except NoSuchElementException:
                profile_data['external_url'] = ""

            # Get full name
            try:
                name_element = self.driver.find_element(
                    By.XPATH, "//div[@data-testid='user-name']"
                )
                profile_data['full_name'] = name_element.text
            except NoSuchElementException:
                profile_data['full_name'] = ""

            profile_data['username'] = username
            return profile_data

        except TimeoutException:
            self.logger.error(f"Timeout loading profile: {username}")
            return None
        except Exception as e:
            self.logger.error(f"Error with Selenium for {username}: {e}")
            return None

    def _extract_profile_from_meta(self, soup: BeautifulSoup, username: str) -> Optional[Dict]:
        """Extract profile data from meta tags as fallback"""
        try:
            profile_data = {'username': username}

            # Try to get data from meta tags
            description_meta = soup.find('meta', {'name': 'description'})
            if description_meta:
                content = description_meta.get('content', '')
                # Parse follower count from description
                follower_match = re.search(r'([\d,]+)\s+Followers', content)
                if follower_match:
                    profile_data['followers'] = self._parse_count(follower_match.group(1))

                following_match = re.search(r'([\d,]+)\s+Following', content)
                if following_match:
                    profile_data['following'] = self._parse_count(following_match.group(1))

                posts_match = re.search(r'([\d,]+)\s+Posts', content)
                if posts_match:
                    profile_data['posts'] = self._parse_count(posts_match.group(1))

            # Try to get bio from page content
            bio_elements = soup.find_all('meta', {'property': 'og:description'})
            if bio_elements:
                profile_data['bio'] = bio_elements[0].get('content', '')

            return profile_data if profile_data.get('followers') else None

        except Exception as e:
            self.logger.error(f"Error extracting meta data for {username}: {e}")
            return None

    def _parse_profile_data(self, data: Dict, username: str) -> Dict:
        """Parse profile data from Instagram API response"""
        try:
            return {
                'username': username,
                'full_name': data.get('full_name', ''),
                'bio': data.get('biography', ''),
                'followers': data.get('edge_followed_by', {}).get('count', 0),
                'following': data.get('edge_follow', {}).get('count', 0),
                'posts': data.get('edge_owner_to_timeline_media', {}).get('count', 0),
                'external_url': data.get('external_url', ''),
                'is_private': data.get('is_private', False),
                'is_verified': data.get('is_verified', False),
                'profile_pic_url': data.get('profile_pic_url_hd', ''),
            }
        except Exception as e:
            self.logger.error(f"Error parsing profile data: {e}")
            return {'username': username}

    def _parse_count(self, count_str: str) -> int:
        """Parse follower/following count strings like '1.2M', '15.3K', etc."""
        if not count_str:
            return 0

        # Remove commas and spaces
        count_str = count_str.replace(',', '').replace(' ', '').upper()

        try:
            if 'M' in count_str:
                return int(float(count_str.replace('M', '')) * 1000000)
            elif 'K' in count_str:
                return int(float(count_str.replace('K', '')) * 1000)
            else:
                return int(count_str)
        except (ValueError, TypeError):
            return 0

    def get_recent_posts(self, username: str, count: int = POSTS_TO_ANALYZE) -> List[Dict]:
        """Get recent posts for engagement analysis"""
        url = f"{INSTAGRAM_BASE_URL}/{username}/"

        if self.use_selenium:
            return self._get_posts_selenium(username, count)
        else:
            return self._get_posts_requests(url, count)

    def _get_posts_requests(self, url: str, count: int) -> List[Dict]:
        """Get posts using requests method"""
        response = self._make_request(url)
        if not response:
            return []

        try:
            soup = BeautifulSoup(response.text, 'html.parser')
            script_tags = soup.find_all('script', type='text/javascript')

            for script in script_tags:
                if script.string and 'window._sharedData' in script.string:
                    json_text = script.string
                    start = json_text.find('window._sharedData = ') + len('window._sharedData = ')
                    end = json_text.find(';</script>')
                    if end == -1:
                        end = json_text.find(';', start)

                    try:
                        shared_data = json.loads(json_text[start:end])
                        entry_data = shared_data.get('entry_data', {})
                        profile_page = entry_data.get('ProfilePage', [])

                        if profile_page:
                            user_data = profile_page[0]['graphql']['user']
                            posts_data = user_data.get('edge_owner_to_timeline_media', {}).get('edges', [])

                            posts = []
                            for post_edge in posts_data[:count]:
                                post = post_edge['node']
                                posts.append({
                                    'id': post.get('id'),
                                    'shortcode': post.get('shortcode'),
                                    'likes': post.get('edge_liked_by', {}).get('count', 0),
                                    'comments': post.get('edge_media_to_comment', {}).get('count', 0),
                                    'caption': self._extract_caption(post),
                                    'timestamp': post.get('taken_at_timestamp'),
                                    'url': f"{INSTAGRAM_BASE_URL}/p/{post.get('shortcode')}/"
                                })

                            return posts
                    except (json.JSONDecodeError, KeyError, IndexError):
                        continue

            return []

        except Exception as e:
            self.logger.error(f"Error getting posts: {e}")
            return []

    def _get_posts_selenium(self, username: str, count: int) -> List[Dict]:
        """Get posts using Selenium method"""
        if not self.driver:
            return []

        try:
            url = f"{INSTAGRAM_BASE_URL}/{username}/"
            self.driver.get(url)
            time.sleep(3)

            # Wait for posts to load
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "article"))
            )

            posts = []
            post_links = self.driver.find_elements(By.XPATH, "//article//a[contains(@href, '/p/')]")

            for i, link in enumerate(post_links[:count]):
                try:
                    post_url = link.get_attribute('href')
                    shortcode = post_url.split('/p/')[1].rstrip('/')

                    # Click on post to get details
                    link.click()
                    time.sleep(2)

                    # Extract likes and comments
                    likes = 0
                    comments = 0

                    try:
                        likes_element = self.driver.find_element(
                            By.XPATH, "//button[contains(@class, 'wpO6b')]//span"
                        )
                        likes = self._parse_count(likes_element.text)
                    except NoSuchElementException:
                        pass

                    try:
                        comments_elements = self.driver.find_elements(
                            By.XPATH, "//div[@role='button']//span[contains(text(), 'comment')]"
                        )
                        if comments_elements:
                            comments = len(comments_elements)
                    except NoSuchElementException:
                        pass

                    # Extract caption
                    caption = ""
                    try:
                        caption_element = self.driver.find_element(
                            By.XPATH, "//div[@data-testid='post-caption']"
                        )
                        caption = caption_element.text
                    except NoSuchElementException:
                        pass

                    posts.append({
                        'shortcode': shortcode,
                        'likes': likes,
                        'comments': comments,
                        'caption': caption,
                        'url': post_url
                    })

                    # Close post modal
                    self.driver.find_element(By.XPATH, "//button[@aria-label='Close']").click()
                    time.sleep(1)

                except Exception as e:
                    self.logger.error(f"Error processing post {i}: {e}")
                    continue

            return posts

        except Exception as e:
            self.logger.error(f"Error getting posts with Selenium: {e}")
            return []

    def _extract_caption(self, post_data: Dict) -> str:
        """Extract caption from post data"""
        try:
            caption_edges = post_data.get('edge_media_to_caption', {}).get('edges', [])
            if caption_edges:
                return caption_edges[0]['node']['text']
        except (KeyError, IndexError):
            pass
        return ""

    def scrape_hashtag(self, hashtag: str, max_posts: int = 50) -> List[str]:
        """Scrape usernames from hashtag posts"""
        hashtag = hashtag.replace('#', '')
        url = f"{INSTAGRAM_EXPLORE_TAGS_URL}/{hashtag}/"

        response = self._make_request(url)
        if not response:
            return []

        try:
            soup = BeautifulSoup(response.text, 'html.parser')
            usernames = set()

            # Extract usernames from various elements
            links = soup.find_all('a', href=re.compile(r'^/[^/]+/$'))
            for link in links:
                href = link.get('href', '')
                username = href.strip('/').split('/')[-1]
                if username and not username.startswith('explore') and not username.startswith('p'):
                    usernames.add(username)

            return list(usernames)[:max_posts]

        except Exception as e:
            self.logger.error(f"Error scraping hashtag {hashtag}: {e}")
            return []

    def close(self):
        """Clean up resources"""
        if self.driver:
            self.driver.quit()
        self.session.close()
