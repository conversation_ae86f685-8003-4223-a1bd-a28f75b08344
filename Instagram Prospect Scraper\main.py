"""
Main execution script for Instagram Prospect Scraper
"""

import argparse
import logging
import time
import sys
from typing import List, Dict
from instagram_scraper import InstagramScraper
from prospect_analyzer import ProspectAnalyzer
from data_exporter import DataExporter
from config import *

class InstagramProspectScraper:
    """Main class orchestrating the scraping process"""
    
    def __init__(self, use_selenium: bool = False):
        self.scraper = InstagramScraper(use_selenium=use_selenium)
        self.analyzer = ProspectAnalyzer()
        self.exporter = DataExporter()
        self.logger = logging.getLogger(__name__)
        
        # Setup logging
        logging.basicConfig(
            level=getattr(logging, LOG_LEVEL),
            format=LOG_FORMAT,
            handlers=[
                logging.FileHandler(LOG_FILE),
                logging.StreamHandler()
            ]
        )
    
    def scrape_from_usernames(self, usernames: List[str]) -> List[Dict]:
        """Scrape prospects from a list of usernames"""
        prospects = []
        
        self.logger.info(f"Starting to scrape {len(usernames)} usernames")
        
        for i, username in enumerate(usernames, 1):
            try:
                self.logger.info(f"Processing {i}/{len(usernames)}: {username}")
                
                # Get profile data
                profile_data = self.scraper.get_profile_data(username)
                if not profile_data:
                    self.logger.warning(f"Could not get profile data for {username}")
                    continue
                
                # Check basic criteria first
                if not self._meets_basic_criteria(profile_data):
                    self.logger.info(f"Profile {username} doesn't meet basic criteria, skipping")
                    continue
                
                # Get recent posts for engagement analysis
                posts_data = self.scraper.get_recent_posts(username)
                
                # Analyze prospect
                analysis = self.analyzer.analyze_profile(profile_data, posts_data)
                prospects.append(analysis)
                
                self.logger.info(f"Analyzed {username}: Qualified={analysis.get('qualified', False)}")
                
                # Rate limiting
                time.sleep(REQUEST_DELAY)
                
            except Exception as e:
                self.logger.error(f"Error processing {username}: {e}")
                continue
        
        self.logger.info(f"Completed scraping. Found {len(prospects)} prospects")
        return prospects
    
    def scrape_from_hashtags(self, hashtags: List[str], max_per_hashtag: int = 50) -> List[Dict]:
        """Scrape prospects from hashtags"""
        all_usernames = set()
        
        self.logger.info(f"Scraping usernames from {len(hashtags)} hashtags")
        
        for hashtag in hashtags:
            try:
                self.logger.info(f"Scraping hashtag: {hashtag}")
                usernames = self.scraper.scrape_hashtag(hashtag, max_per_hashtag)
                all_usernames.update(usernames)
                self.logger.info(f"Found {len(usernames)} usernames from {hashtag}")
                
                time.sleep(REQUEST_DELAY)
                
            except Exception as e:
                self.logger.error(f"Error scraping hashtag {hashtag}: {e}")
                continue
        
        self.logger.info(f"Total unique usernames from hashtags: {len(all_usernames)}")
        
        # Now scrape the found usernames
        return self.scrape_from_usernames(list(all_usernames))
    
    def _meets_basic_criteria(self, profile_data: Dict) -> bool:
        """Check if profile meets basic criteria before detailed analysis"""
        followers = profile_data.get('followers', 0)
        is_private = profile_data.get('is_private', True)
        
        # Check follower count and privacy
        if followers < FOLLOWER_COUNT_MIN or followers > FOLLOWER_COUNT_MAX:
            return False
        
        if is_private:
            return False
        
        # Check if bio contains any niche keywords
        bio = profile_data.get('bio', '').lower()
        has_niche_keyword = any(keyword.lower() in bio for keyword in NICHE_KEYWORDS)
        
        return has_niche_keyword
    
    def run_full_analysis(self, prospects: List[Dict]) -> Dict:
        """Run full analysis and export results"""
        if not prospects:
            self.logger.warning("No prospects to analyze")
            return {}
        
        # Rank prospects by quality
        ranked_prospects = self.analyzer.rank_prospects(prospects)
        
        # Filter qualified prospects
        qualified_prospects = self.analyzer.filter_qualified_prospects(ranked_prospects)
        
        # Export results
        csv_file = self.exporter.export_to_csv(ranked_prospects)
        excel_file = self.exporter.export_to_excel(ranked_prospects)
        
        # Generate summary
        summary = {
            'total_prospects': len(prospects),
            'qualified_prospects': len(qualified_prospects),
            'qualification_rate': (len(qualified_prospects) / len(prospects) * 100) if prospects else 0,
            'csv_file': csv_file,
            'excel_file': excel_file,
            'top_prospects': qualified_prospects[:10]  # Top 10 qualified prospects
        }
        
        return summary
    
    def close(self):
        """Clean up resources"""
        self.scraper.close()

def main():
    """Main function with CLI interface"""
    parser = argparse.ArgumentParser(description='Instagram Prospect Scraper')
    parser.add_argument('--mode', choices=['usernames', 'hashtags', 'mixed'], 
                       default='hashtags', help='Scraping mode')
    parser.add_argument('--usernames', nargs='+', help='List of usernames to scrape')
    parser.add_argument('--hashtags', nargs='+', help='List of hashtags to scrape')
    parser.add_argument('--max-per-hashtag', type=int, default=50, 
                       help='Maximum usernames per hashtag')
    parser.add_argument('--use-selenium', action='store_true', 
                       help='Use Selenium for scraping (more reliable but slower)')
    parser.add_argument('--output-csv', help='Custom CSV output filename')
    parser.add_argument('--output-excel', help='Custom Excel output filename')
    
    args = parser.parse_args()
    
    # Initialize scraper
    scraper_app = InstagramProspectScraper(use_selenium=args.use_selenium)
    
    try:
        prospects = []
        
        if args.mode == 'usernames':
            if not args.usernames:
                print("Error: --usernames required for usernames mode")
                sys.exit(1)
            prospects = scraper_app.scrape_from_usernames(args.usernames)
            
        elif args.mode == 'hashtags':
            hashtags = args.hashtags or TARGET_HASHTAGS[:5]  # Use default hashtags if none provided
            prospects = scraper_app.scrape_from_hashtags(hashtags, args.max_per_hashtag)
            
        elif args.mode == 'mixed':
            # Scrape from both usernames and hashtags
            if args.usernames:
                prospects.extend(scraper_app.scrape_from_usernames(args.usernames))
            
            hashtags = args.hashtags or TARGET_HASHTAGS[:3]
            prospects.extend(scraper_app.scrape_from_hashtags(hashtags, args.max_per_hashtag))
        
        # Run analysis and export
        if prospects:
            summary = scraper_app.run_full_analysis(prospects)
            
            # Print summary
            print("\n" + "="*50)
            print("SCRAPING SUMMARY")
            print("="*50)
            print(f"Total prospects analyzed: {summary['total_prospects']}")
            print(f"Qualified prospects: {summary['qualified_prospects']}")
            print(f"Qualification rate: {summary['qualification_rate']:.1f}%")
            print(f"CSV file: {summary['csv_file']}")
            print(f"Excel file: {summary['excel_file']}")
            
            if summary['top_prospects']:
                print("\nTop 5 Qualified Prospects:")
                print("-" * 30)
                for i, prospect in enumerate(summary['top_prospects'][:5], 1):
                    print(f"{i}. @{prospect['username']} - "
                          f"Followers: {prospect['follower_count']:,} - "
                          f"Engagement: {prospect['engagement_rate']:.1f}% - "
                          f"Score: {prospect.get('quality_score', 0):.1f}")
            
        else:
            print("No prospects found matching the criteria.")
    
    except KeyboardInterrupt:
        print("\nScraping interrupted by user")
    except Exception as e:
        print(f"Error during scraping: {e}")
        logging.error(f"Fatal error: {e}", exc_info=True)
    finally:
        scraper_app.close()

if __name__ == "__main__":
    main()
